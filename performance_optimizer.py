"""
Performance optimization and monitoring for the Liquidity Pump Detection Bot
"""
import asyncio
import time
import psutil
import gc
from datetime import datetime, timedelta
from typing import Dict, List, Any
from loguru import logger
import sqlite3
from pathlib import Path

from config import Config

class PerformanceOptimizer:
    """Monitor and optimize bot performance"""
    
    def __init__(self):
        self.db_path = Config.DATABASE_PATH
        self.performance_data = []
        self.last_cleanup = datetime.now()
        self._initialize_performance_db()
    
    def _initialize_performance_db(self):
        """Initialize performance monitoring database"""
        try:
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    cpu_percent REAL,
                    memory_percent REAL,
                    memory_mb REAL,
                    api_calls_count INTEGER,
                    response_time_ms REAL,
                    active_connections INTEGER,
                    database_size_mb REAL
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS error_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    error_type TEXT,
                    error_message TEXT,
                    component TEXT,
                    severity TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("✅ Performance monitoring database initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize performance database: {e}")
    
    async def monitor_performance(self):
        """Monitor system performance metrics"""
        try:
            # Get system metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_mb = memory.used / (1024 * 1024)
            
            # Get database size
            db_size_mb = 0
            if Path(self.db_path).exists():
                db_size_mb = Path(self.db_path).stat().st_size / (1024 * 1024)
            
            # Store metrics
            await self._store_performance_metrics({
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'memory_mb': memory_mb,
                'database_size_mb': db_size_mb,
                'api_calls_count': 0,  # Will be updated by API calls
                'response_time_ms': 0,  # Will be updated by API calls
                'active_connections': 0  # Will be updated by connection pool
            })
            
            # Check for performance issues
            await self._check_performance_alerts(cpu_percent, memory_percent, db_size_mb)
            
        except Exception as e:
            logger.error(f"❌ Error monitoring performance: {e}")
    
    async def _store_performance_metrics(self, metrics: Dict[str, Any]):
        """Store performance metrics in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO performance_metrics 
                (cpu_percent, memory_percent, memory_mb, api_calls_count, 
                 response_time_ms, active_connections, database_size_mb)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                metrics['cpu_percent'],
                metrics['memory_percent'],
                metrics['memory_mb'],
                metrics['api_calls_count'],
                metrics['response_time_ms'],
                metrics['active_connections'],
                metrics['database_size_mb']
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Error storing performance metrics: {e}")
    
    async def _check_performance_alerts(self, cpu_percent: float, memory_percent: float, db_size_mb: float):
        """Check for performance issues and log alerts"""
        alerts = []
        
        if cpu_percent > 80:
            alerts.append(f"⚠️ High CPU usage: {cpu_percent:.1f}%")
        
        if memory_percent > 85:
            alerts.append(f"⚠️ High memory usage: {memory_percent:.1f}%")
        
        if db_size_mb > 100:  # 100MB database size warning
            alerts.append(f"⚠️ Large database size: {db_size_mb:.1f}MB")
        
        for alert in alerts:
            logger.warning(alert)
            await self._log_error("PERFORMANCE_WARNING", alert, "PerformanceOptimizer", "WARNING")
    
    async def _log_error(self, error_type: str, message: str, component: str, severity: str):
        """Log error to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO error_log (error_type, error_message, component, severity)
                VALUES (?, ?, ?, ?)
            ''', (error_type, message, component, severity))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Error logging to database: {e}")
    
    async def optimize_database(self):
        """Optimize database performance"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Clean old data (keep last 30 days)
            thirty_days_ago = datetime.now() - timedelta(days=30)
            
            # Clean old performance metrics
            cursor.execute('''
                DELETE FROM performance_metrics 
                WHERE timestamp < ?
            ''', (thirty_days_ago,))
            
            # Clean old market data
            cursor.execute('''
                DELETE FROM market_data 
                WHERE timestamp < ?
            ''', (thirty_days_ago,))
            
            # Clean old error logs (keep last 7 days)
            seven_days_ago = datetime.now() - timedelta(days=7)
            cursor.execute('''
                DELETE FROM error_log 
                WHERE timestamp < ?
            ''', (seven_days_ago,))
            
            # Vacuum database to reclaim space
            cursor.execute('VACUUM')
            
            # Analyze tables for better query performance
            cursor.execute('ANALYZE')
            
            conn.commit()
            conn.close()
            
            logger.info("✅ Database optimization completed")
            
        except Exception as e:
            logger.error(f"❌ Error optimizing database: {e}")
    
    async def cleanup_memory(self):
        """Perform memory cleanup"""
        try:
            # Force garbage collection
            collected = gc.collect()
            
            # Log memory cleanup
            memory = psutil.virtual_memory()
            logger.info(f"🧹 Memory cleanup: {collected} objects collected, {memory.percent:.1f}% memory used")
            
            self.last_cleanup = datetime.now()
            
        except Exception as e:
            logger.error(f"❌ Error during memory cleanup: {e}")
    
    async def get_performance_report(self) -> Dict[str, Any]:
        """Generate performance report"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get recent metrics (last hour)
            one_hour_ago = datetime.now() - timedelta(hours=1)
            cursor.execute('''
                SELECT AVG(cpu_percent), AVG(memory_percent), AVG(memory_mb),
                       MAX(cpu_percent), MAX(memory_percent), COUNT(*)
                FROM performance_metrics 
                WHERE timestamp > ?
            ''', (one_hour_ago,))
            
            metrics = cursor.fetchone()
            
            # Get error count
            cursor.execute('''
                SELECT COUNT(*) FROM error_log 
                WHERE timestamp > ?
            ''', (one_hour_ago,))
            
            error_count = cursor.fetchone()[0]
            
            # Get database size
            cursor.execute('SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()')
            db_size = cursor.fetchone()[0] / (1024 * 1024)  # Convert to MB
            
            conn.close()
            
            if metrics and metrics[0] is not None:
                return {
                    'avg_cpu_percent': round(metrics[0], 2),
                    'avg_memory_percent': round(metrics[1], 2),
                    'avg_memory_mb': round(metrics[2], 2),
                    'max_cpu_percent': round(metrics[3], 2),
                    'max_memory_percent': round(metrics[4], 2),
                    'data_points': metrics[5],
                    'error_count': error_count,
                    'database_size_mb': round(db_size, 2),
                    'last_cleanup': self.last_cleanup.strftime('%Y-%m-%d %H:%M:%S')
                }
            else:
                return {
                    'avg_cpu_percent': 0,
                    'avg_memory_percent': 0,
                    'avg_memory_mb': 0,
                    'max_cpu_percent': 0,
                    'max_memory_percent': 0,
                    'data_points': 0,
                    'error_count': error_count,
                    'database_size_mb': round(db_size, 2),
                    'last_cleanup': self.last_cleanup.strftime('%Y-%m-%d %H:%M:%S')
                }
            
        except Exception as e:
            logger.error(f"❌ Error generating performance report: {e}")
            return {}
    
    async def run_optimization_cycle(self):
        """Run a complete optimization cycle"""
        try:
            logger.info("🔧 Starting optimization cycle...")
            
            # Monitor current performance
            await self.monitor_performance()
            
            # Check if cleanup is needed (every 6 hours)
            if datetime.now() - self.last_cleanup > timedelta(hours=6):
                await self.cleanup_memory()
                await self.optimize_database()
            
            # Generate and log performance report
            report = await self.get_performance_report()
            if report:
                logger.info(f"📊 Performance Report: CPU {report['avg_cpu_percent']}%, Memory {report['avg_memory_percent']}%, DB {report['database_size_mb']}MB")
            
            logger.info("✅ Optimization cycle completed")
            
        except Exception as e:
            logger.error(f"❌ Error in optimization cycle: {e}")

# Performance monitoring decorator
def monitor_api_call(func):
    """Decorator to monitor API call performance"""
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # Convert to milliseconds
            
            # Log performance if it's slow
            if response_time > 5000:  # 5 seconds
                logger.warning(f"⚠️ Slow API call: {func.__name__} took {response_time:.0f}ms")
            
            return result
        except Exception as e:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            logger.error(f"❌ API call failed: {func.__name__} after {response_time:.0f}ms - {e}")
            raise
    
    return wrapper
