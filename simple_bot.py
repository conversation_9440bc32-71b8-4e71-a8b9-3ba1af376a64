"""
Simplified version of the Liquidity Pump Detection Bot
"""
import asyncio
import os
import sys
from pathlib import Path
from datetime import datetime
from loguru import logger

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from src.market_monitor import MarketMonitor
from src.pump_detector import PumpDetector
from src.user_settings import UserSettingsManager
from telegram import Bot

class SimpleTelegramBot:
    """Simplified Telegram bot without Application framework"""
    
    def __init__(self):
        self.bot_token = Config.TELEGRAM_BOT_TOKEN
        self.chat_id = Config.TELEGRAM_CHAT_ID
        self.bot = None
        self._initialize_bot()
    
    def _initialize_bot(self):
        """Initialize the Telegram bot"""
        try:
            if not self.bot_token:
                raise ValueError("TELEGRAM_BOT_TOKEN is required")
            
            self.bot = Bot(token=self.bot_token)
            logger.info("✅ Telegram bot initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Telegram bot: {e}")
            raise
    
    async def send_pump_alert(self, pump_data):
        """Send a pump alert to the configured chat"""
        try:
            message = self._format_pump_message(pump_data)
            
            if self.chat_id:
                await self.bot.send_message(
                    chat_id=self.chat_id,
                    text=message,
                    parse_mode='Markdown'
                )
                logger.info(f"📤 Pump alert sent for {pump_data.get('symbol', 'Unknown')}")
            else:
                logger.warning("⚠️ No chat ID configured, cannot send alert")
                
        except Exception as e:
            logger.error(f"❌ Failed to send pump alert: {e}")
    
    def _format_pump_message(self, pump_data):
        """Format pump data into a readable message"""
        symbol = pump_data.get('symbol', 'Unknown')
        market_type = pump_data.get('market_type', 'Unknown')
        price_change = pump_data.get('price_change_percent', 0)
        volume_spike = pump_data.get('volume_spike', 0)
        current_price = pump_data.get('current_price', 0)
        volume = pump_data.get('volume', 0)
        timestamp = pump_data.get('timestamp', datetime.now())
        
        # Determine emoji based on price change
        trend_emoji = "🚀" if price_change > 0 else "📉"
        market_emoji = "🪙" if market_type == "crypto" else "💱"
        
        message = f"""
{trend_emoji} **تنبيه ضخ سيولة!** {market_emoji}

**الرمز:** {symbol}
**السوق:** {market_type.upper()}
**السعر الحالي:** {current_price:.6f}
**تغيير السعر:** {price_change:+.2f}%
**زيادة الحجم:** {volume_spike:.1f}x
**الحجم:** {volume:,.0f}

⏰ **الوقت:** {timestamp.strftime('%H:%M:%S')}
📅 **التاريخ:** {timestamp.strftime('%Y-%m-%d')}

#ضخ_سيولة #{symbol.replace('/', '_')} #{market_type}
        """
        
        return message.strip()
    
    async def send_startup_message(self):
        """Send startup message"""
        try:
            message = """
🚀 **بوت مراقبة ضخ السيولة**

✅ البوت يعمل الآن!
📊 يراقب العملات الرقمية والفوركس
🔔 سيرسل تنبيهات عند اكتشاف ضخ السيولة

**المراقبة النشطة:**
🪙 العملات الرقمية: BTC, ETH, BNB وغيرها
💱 الفوركس: EUR/USD, GBP/USD وغيرها

البوت جاهز! 🎉
            """
            
            if self.chat_id:
                await self.bot.send_message(
                    chat_id=self.chat_id,
                    text=message,
                    parse_mode='Markdown'
                )
                logger.info("✅ Startup message sent")
        except Exception as e:
            logger.error(f"❌ Failed to send startup message: {e}")

async def setup_logging():
    """Setup logging configuration"""
    logger.remove()  # Remove default handler
    
    # Add file handler
    logger.add(
        Config.LOG_FILE,
        level=Config.LOG_LEVEL,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        rotation="1 day",
        retention="7 days"
    )
    
    # Add console handler
    logger.add(
        sys.stdout,
        level=Config.LOG_LEVEL,
        format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | <level>{message}</level>"
    )

async def run_monitoring_loop(telegram_bot, market_monitor, pump_detector, user_settings):
    """Main monitoring loop"""
    logger.info("🔄 Starting monitoring loop")
    
    while True:
        try:
            # Get market data
            logger.info("📊 Fetching market data...")
            crypto_data = await market_monitor.get_crypto_data()
            forex_data = await market_monitor.get_forex_data()
            
            # Detect pumps
            crypto_pumps = pump_detector.detect_crypto_pumps(crypto_data)
            forex_pumps = pump_detector.detect_forex_pumps(forex_data)
            
            total_pumps = len(crypto_pumps) + len(forex_pumps)
            if total_pumps > 0:
                logger.info(f"🚨 Detected {total_pumps} pumps!")
            
            # Send alerts
            for pump in crypto_pumps + forex_pumps:
                if Config.TELEGRAM_CHAT_ID:
                    user_id = Config.TELEGRAM_CHAT_ID
                    if user_settings.should_send_alert(user_id, pump):
                        await telegram_bot.send_pump_alert(pump)
                        user_settings.record_alert_sent(user_id, pump, "Pump alert sent")
                        pump_detector.record_pump_alert(pump)
                else:
                    await telegram_bot.send_pump_alert(pump)
                    pump_detector.record_pump_alert(pump)
            
            logger.info(f"✅ Monitoring cycle completed. Waiting {Config.CHECK_INTERVAL_SECONDS} seconds...")
            await asyncio.sleep(Config.CHECK_INTERVAL_SECONDS)
            
        except Exception as e:
            logger.error(f"❌ Error in monitoring loop: {e}")
            await asyncio.sleep(30)  # Wait 30 seconds before retrying

async def main():
    """Main function"""
    try:
        # Setup logging
        await setup_logging()
        
        logger.info("🚀 Starting Simple Liquidity Pump Detection Bot")
        
        # Validate configuration
        if not Config.validate_config():
            logger.error("❌ Configuration validation failed")
            return
        
        # Initialize components
        telegram_bot = SimpleTelegramBot()
        market_monitor = MarketMonitor()
        pump_detector = PumpDetector()
        user_settings = UserSettingsManager()
        
        # Send startup message
        await telegram_bot.send_startup_message()
        
        logger.info("✅ Bot initialized successfully")
        logger.info(f"📊 Monitoring {len(Config.CRYPTO_PAIRS)} crypto pairs and {len(Config.FOREX_PAIRS)} forex pairs")
        
        # Run the monitoring loop
        await run_monitoring_loop(telegram_bot, market_monitor, pump_detector, user_settings)
        
    except KeyboardInterrupt:
        logger.info("🛑 Bot stopped by user")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
