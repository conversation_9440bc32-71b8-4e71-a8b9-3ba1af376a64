"""
User settings management for the Liquidity Pump Detection Bot
"""
import sqlite3
import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path
from loguru import logger

from config import Config

class UserSettingsManager:
    """Manage user-specific settings and preferences"""
    
    def __init__(self):
        self.db_path = Config.DATABASE_PATH
        self._initialize_user_tables()
    
    def _initialize_user_tables(self):
        """Initialize user-related database tables"""
        try:
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # User settings table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_settings (
                    user_id TEXT PRIMARY KEY,
                    username TEXT,
                    chat_id TEXT,
                    settings JSON NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # User subscriptions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_subscriptions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    market_type TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(user_id, symbol, market_type)
                )
            ''')
            
            # User alert history
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_alert_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    market_type TEXT NOT NULL,
                    alert_type TEXT NOT NULL,
                    message TEXT,
                    sent_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("✅ User settings database initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize user settings database: {e}")
    
    def get_user_settings(self, user_id: str) -> Dict[str, Any]:
        """Get user settings or create default settings"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT settings FROM user_settings WHERE user_id = ?', (user_id,))
            result = cursor.fetchone()
            
            if result:
                settings = json.loads(result[0])
            else:
                # Create default settings
                settings = self._get_default_settings()
                self._create_user_settings(user_id, settings)
            
            conn.close()
            return settings
            
        except Exception as e:
            logger.error(f"❌ Error getting user settings: {e}")
            return self._get_default_settings()
    
    def _get_default_settings(self) -> Dict[str, Any]:
        """Get default user settings"""
        return {
            'notifications': {
                'crypto_enabled': True,
                'forex_enabled': True,
                'volume_threshold': 3.0,
                'price_threshold': 5.0,
                'min_confidence': 0.7,
                'quiet_hours': {
                    'enabled': False,
                    'start': '22:00',
                    'end': '08:00'
                }
            },
            'display': {
                'language': 'ar',  # Arabic
                'timezone': 'UTC',
                'price_format': 'auto',
                'include_charts': False
            },
            'filters': {
                'min_market_cap': 0,
                'max_alerts_per_hour': 10,
                'blacklisted_symbols': [],
                'whitelisted_symbols': []
            },
            'advanced': {
                'technical_analysis': False,
                'sentiment_analysis': False,
                'news_integration': False
            }
        }
    
    def _create_user_settings(self, user_id: str, settings: Dict[str, Any]):
        """Create new user settings"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO user_settings (user_id, settings, updated_at)
                VALUES (?, ?, ?)
            ''', (user_id, json.dumps(settings), datetime.now()))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Error creating user settings: {e}")
    
    def update_user_settings(self, user_id: str, settings: Dict[str, Any]) -> bool:
        """Update user settings"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE user_settings 
                SET settings = ?, updated_at = ?
                WHERE user_id = ?
            ''', (json.dumps(settings), datetime.now(), user_id))
            
            if cursor.rowcount == 0:
                # User doesn't exist, create new
                self._create_user_settings(user_id, settings)
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Updated settings for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error updating user settings: {e}")
            return False
    
    def update_user_preference(self, user_id: str, key_path: str, value: Any) -> bool:
        """Update a specific user preference"""
        try:
            settings = self.get_user_settings(user_id)
            
            # Navigate to the nested key
            keys = key_path.split('.')
            current = settings
            
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]
            
            current[keys[-1]] = value
            
            return self.update_user_settings(user_id, settings)
            
        except Exception as e:
            logger.error(f"❌ Error updating user preference: {e}")
            return False
    
    def get_user_subscriptions(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user's symbol subscriptions"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT symbol, market_type, is_active, created_at
                FROM user_subscriptions 
                WHERE user_id = ? AND is_active = TRUE
            ''', (user_id,))
            
            results = cursor.fetchall()
            conn.close()
            
            return [
                {
                    'symbol': row[0],
                    'market_type': row[1],
                    'is_active': bool(row[2]),
                    'created_at': row[3]
                }
                for row in results
            ]
            
        except Exception as e:
            logger.error(f"❌ Error getting user subscriptions: {e}")
            return []
    
    def add_user_subscription(self, user_id: str, symbol: str, market_type: str) -> bool:
        """Add a symbol subscription for user"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO user_subscriptions 
                (user_id, symbol, market_type, is_active)
                VALUES (?, ?, ?, TRUE)
            ''', (user_id, symbol, market_type))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Added subscription for {symbol} to user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error adding user subscription: {e}")
            return False
    
    def remove_user_subscription(self, user_id: str, symbol: str, market_type: str) -> bool:
        """Remove a symbol subscription for user"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE user_subscriptions 
                SET is_active = FALSE
                WHERE user_id = ? AND symbol = ? AND market_type = ?
            ''', (user_id, symbol, market_type))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Removed subscription for {symbol} from user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error removing user subscription: {e}")
            return False
    
    def should_send_alert(self, user_id: str, pump_data: Dict[str, Any]) -> bool:
        """Check if alert should be sent to user based on their settings"""
        try:
            settings = self.get_user_settings(user_id)
            
            # Check if notifications are enabled for this market type
            market_type = pump_data.get('market_type', 'crypto')
            if market_type == 'crypto' and not settings['notifications']['crypto_enabled']:
                return False
            if market_type == 'forex' and not settings['notifications']['forex_enabled']:
                return False
            
            # Check thresholds
            volume_spike = pump_data.get('volume_spike', 0)
            price_change = abs(pump_data.get('price_change_percent', 0))
            confidence = pump_data.get('confidence', 0)
            
            if volume_spike < settings['notifications']['volume_threshold']:
                return False
            if price_change < settings['notifications']['price_threshold']:
                return False
            if confidence < settings['notifications']['min_confidence']:
                return False
            
            # Check quiet hours
            if settings['notifications']['quiet_hours']['enabled']:
                current_time = datetime.now().strftime('%H:%M')
                start_time = settings['notifications']['quiet_hours']['start']
                end_time = settings['notifications']['quiet_hours']['end']
                
                if start_time <= current_time <= end_time:
                    return False
            
            # Check filters
            symbol = pump_data.get('symbol', '')
            blacklist = settings['filters']['blacklisted_symbols']
            whitelist = settings['filters']['whitelisted_symbols']
            
            if symbol in blacklist:
                return False
            if whitelist and symbol not in whitelist:
                return False
            
            # Check rate limiting
            if not self._check_rate_limit(user_id, settings['filters']['max_alerts_per_hour']):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error checking alert conditions: {e}")
            return True  # Default to sending alert
    
    def _check_rate_limit(self, user_id: str, max_alerts_per_hour: int) -> bool:
        """Check if user hasn't exceeded alert rate limit"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            one_hour_ago = datetime.now() - timedelta(hours=1)
            cursor.execute('''
                SELECT COUNT(*) FROM user_alert_history 
                WHERE user_id = ? AND sent_at > ?
            ''', (user_id, one_hour_ago))
            
            alert_count = cursor.fetchone()[0]
            conn.close()
            
            return alert_count < max_alerts_per_hour
            
        except Exception as e:
            logger.error(f"❌ Error checking rate limit: {e}")
            return True
    
    def record_alert_sent(self, user_id: str, pump_data: Dict[str, Any], message: str):
        """Record that an alert was sent to user"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO user_alert_history 
                (user_id, symbol, market_type, alert_type, message)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                user_id,
                pump_data.get('symbol', ''),
                pump_data.get('market_type', ''),
                'pump_alert',
                message
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Error recording alert: {e}")
    
    def get_user_stats(self, user_id: str) -> Dict[str, Any]:
        """Get user statistics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get alert count
            cursor.execute('''
                SELECT COUNT(*) FROM user_alert_history WHERE user_id = ?
            ''', (user_id,))
            total_alerts = cursor.fetchone()[0]
            
            # Get subscription count
            cursor.execute('''
                SELECT COUNT(*) FROM user_subscriptions 
                WHERE user_id = ? AND is_active = TRUE
            ''', (user_id,))
            active_subscriptions = cursor.fetchone()[0]
            
            # Get recent alerts (last 24 hours)
            cursor.execute('''
                SELECT COUNT(*) FROM user_alert_history 
                WHERE user_id = ? AND sent_at > datetime('now', '-24 hours')
            ''', (user_id,))
            recent_alerts = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'total_alerts': total_alerts,
                'active_subscriptions': active_subscriptions,
                'recent_alerts_24h': recent_alerts,
                'user_id': user_id
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting user stats: {e}")
            return {
                'total_alerts': 0,
                'active_subscriptions': 0,
                'recent_alerts_24h': 0,
                'user_id': user_id
            }
