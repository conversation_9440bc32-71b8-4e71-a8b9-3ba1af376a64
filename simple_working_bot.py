"""
Simple Working Bot - NO Application Framework - NO Timezone Issues!
"""
import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime
from loguru import logger

from telegram import Bo<PERSON>, InlineKeyboardButton, InlineKeyboardMarkup

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from src.market_monitor import MarketMonitor
from src.pump_detector import PumpDetector
from src.user_settings import UserSettingsManager

async def setup_logging():
    """Setup logging configuration"""
    logger.remove()  # Remove default handler
    
    # Add console handler
    logger.add(
        sys.stdout,
        level=Config.LOG_LEVEL,
        format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | <cyan>{name}</cyan> | <level>{message}</level>"
    )

class SimpleWorkingBot:
    """Simple working bot without Application framework"""
    
    def __init__(self):
        self.bot_token = Config.TELEGRAM_BOT_TOKEN
        self.chat_id = Config.TELEGRAM_CHAT_ID
        self.bot = None
        self.user_settings = UserSettingsManager()
        self.market_monitor = MarketMonitor()
        self.pump_detector = PumpDetector()
        self.monitoring_active = True
        self.last_update_id = 0
        self._initialize_bot()
    
    def _initialize_bot(self):
        """Initialize the Telegram bot - SIMPLE VERSION"""
        try:
            self.bot = Bot(token=self.bot_token)
            logger.info("✅ Simple working bot initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize bot: {e}")
            raise
    
    async def send_main_menu(self, chat_id, message_text="🎛️ **القائمة الرئيسية**"):
        """Send main menu with working buttons"""
        status_emoji = "🟢" if self.monitoring_active else "🔴"
        
        keyboard = [
            [
                InlineKeyboardButton("📊 حالة البوت", callback_data="status"),
                InlineKeyboardButton("📈 إحصائياتي", callback_data="mystats")
            ],
            [
                InlineKeyboardButton("🔔 الاشتراكات", callback_data="subscriptions"),
                InlineKeyboardButton("⚙️ الإعدادات", callback_data="settings")
            ],
            [
                InlineKeyboardButton("🛑 إيقاف" if self.monitoring_active else "▶️ تشغيل", 
                                   callback_data="toggle_monitoring")
            ],
            [
                InlineKeyboardButton("❓ المساعدة", callback_data="help"),
                InlineKeyboardButton("🔄 تحديث", callback_data="refresh")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        if message_text == "🎛️ **القائمة الرئيسية**":
            message_text = f"""
🚀 **بوت مراقبة ضخ السيولة**

{status_emoji} **الحالة:** {'يعمل' if self.monitoring_active else 'متوقف'}
📊 **يراقب:** 10 عملات رقمية + 10 فوركس
🔔 **الأزرار تعمل بشكل كامل!**

⏰ **الوقت:** {datetime.now().strftime('%H:%M:%S')}
            """
        
        await self.bot.send_message(
            chat_id=chat_id,
            text=message_text,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )
    
    async def process_updates(self):
        """Process incoming updates"""
        try:
            updates = await self.bot.get_updates(offset=self.last_update_id + 1, timeout=1)
            
            for update in updates:
                self.last_update_id = update.update_id
                
                if update.message:
                    await self.handle_message(update.message)
                elif update.callback_query:
                    await self.handle_callback(update.callback_query)
                    
        except Exception as e:
            if "timed out" not in str(e).lower():
                logger.error(f"❌ Error processing updates: {e}")
    
    async def handle_message(self, message):
        """Handle text messages"""
        try:
            text = message.text
            chat_id = message.chat_id
            user_id = str(message.from_user.id)
            
            if text.startswith('/start') or text.startswith('/menu'):
                await self.send_main_menu(chat_id)
                
            elif text.startswith('/subscribe'):
                await self.handle_subscribe(message)
                
            elif text.startswith('/unsubscribe'):
                await self.handle_unsubscribe(message)
                
            elif text.startswith('/threshold'):
                await self.handle_threshold(message)
                
            elif text.startswith('/stop'):
                self.monitoring_active = False
                await self.bot.send_message(chat_id, "🛑 **تم إيقاف المراقبة**", parse_mode='Markdown')
                
            elif text.startswith('/resume'):
                self.monitoring_active = True
                await self.bot.send_message(chat_id, "▶️ **تم تشغيل المراقبة**", parse_mode='Markdown')
                
            else:
                keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="menu")]]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await self.bot.send_message(
                    chat_id=chat_id,
                    text="🤖 استخدم الأزرار أو الأوامر للتحكم في البوت",
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                
        except Exception as e:
            logger.error(f"❌ Error handling message: {e}")
    
    async def handle_callback(self, callback_query):
        """Handle button callbacks"""
        try:
            data = callback_query.data
            chat_id = callback_query.message.chat_id
            message_id = callback_query.message.message_id
            user_id = str(callback_query.from_user.id)
            
            # Answer the callback query
            await self.bot.answer_callback_query(callback_query.id)
            
            if data == "status":
                await self.show_status(callback_query)
            elif data == "mystats":
                await self.show_mystats(callback_query, user_id)
            elif data == "subscriptions":
                await self.show_subscriptions(callback_query)
            elif data == "settings":
                await self.show_settings(callback_query, user_id)
            elif data == "help":
                await self.show_help(callback_query)
            elif data == "toggle_monitoring":
                await self.toggle_monitoring(callback_query)
            elif data == "refresh":
                await self.refresh_menu(callback_query)
            elif data == "menu" or data == "back":
                await self.back_to_menu(callback_query)
                
        except Exception as e:
            logger.error(f"❌ Error handling callback: {e}")
    
    async def show_status(self, query):
        """Show bot status"""
        status_emoji = "🟢" if self.monitoring_active else "🔴"
        
        message = f"""
📊 **حالة البوت التفصيلية**

{status_emoji} **الحالة:** {'يعمل ويراقب' if self.monitoring_active else 'متوقف'}
⏰ **فترة المراقبة:** كل {Config.CHECK_INTERVAL_SECONDS} ثانية
📈 **العملات الرقمية:** {len(Config.CRYPTO_PAIRS)} عملة
💱 **أزواج الفوركس:** {len(Config.FOREX_PAIRS)} زوج

**الحدود الحالية:**
📊 حد الحجم: {Config.ALERT_THRESHOLDS['volume_spike']}x
📈 حد السعر: {Config.ALERT_THRESHOLDS['price_change']}%

⏰ **الوقت الحالي:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**العملات المراقبة:**
🪙 BTC, ETH, BNB, ADA, SOL, XRP, DOT, DOGE, AVAX, MATIC
💱 EUR/USD, GBP/USD, USD/JPY, USD/CHF, AUD/USD
        """
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="menu")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )
    
    async def show_mystats(self, query, user_id):
        """Show user statistics"""
        stats = self.user_settings.get_user_stats(user_id)
        
        message = f"""
📈 **إحصائياتك الشخصية**

👤 **معرف المستخدم:** {user_id}
🔔 **إجمالي التنبيهات:** {stats['total_alerts']}
📊 **الاشتراكات النشطة:** {stats['active_subscriptions']}
⏰ **تنبيهات آخر 24 ساعة:** {stats['recent_alerts_24h']}

📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d')}
        """
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="menu")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )
    
    async def show_subscriptions(self, query):
        """Show subscription management"""
        message = """
🔔 **إدارة الاشتراكات**

**للاشتراك في عملة رقمية:**
`/subscribe BTC/USDT crypto`
`/subscribe ETH/USDT crypto`

**للاشتراك في الفوركس:**
`/subscribe EUR/USD forex`
`/subscribe GBP/USD forex`

**لإلغاء الاشتراك:**
`/unsubscribe BTC/USDT crypto`

**العملات المتاحة:**

🪙 **العملات الرقمية:**
BTC/USDT, ETH/USDT, BNB/USDT, ADA/USDT, SOL/USDT
XRP/USDT, DOT/USDT, DOGE/USDT, AVAX/USDT, MATIC/USDT

💱 **أزواج الفوركس:**
EUR/USD, GBP/USD, USD/JPY, USD/CHF, AUD/USD
USD/CAD, NZD/USD, EUR/GBP, EUR/JPY, GBP/JPY

**ملاحظة:** أرسل الأوامر كرسائل نصية منفصلة
        """
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="menu")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )
    
    async def show_settings(self, query, user_id):
        """Show settings menu"""
        settings = self.user_settings.get_user_settings(user_id)
        
        message = f"""
⚙️ **إعدادات البوت**

**الحدود الحالية:**
📊 حد الحجم: {settings['notifications']['volume_threshold']}x
📈 حد السعر: {settings['notifications']['price_threshold']}%
🎯 حد الثقة: {settings['notifications']['min_confidence']}

**حالة التنبيهات:**
🪙 العملات الرقمية: {'✅ مفعل' if settings['notifications']['crypto_enabled'] else '❌ معطل'}
💱 الفوركس: {'✅ مفعل' if settings['notifications']['forex_enabled'] else '❌ معطل'}

**لتعديل الحدود أرسل:**
`/threshold volume 3.5` - لتعديل حد الحجم
`/threshold price 7.0` - لتعديل حد السعر  
`/threshold confidence 0.8` - لتعديل حد الثقة
        """
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="menu")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )
    
    async def show_help(self, query):
        """Show help information"""
        message = """
❓ **دليل استخدام البوت**

**🎛️ الأوامر الأساسية:**
/start - بدء البوت وعرض القائمة
/menu - عرض القائمة الرئيسية
/stop - إيقاف المراقبة مؤقتاً
/resume - استئناف المراقبة

**🔔 إدارة الاشتراكات:**
/subscribe [SYMBOL] [TYPE] - الاشتراك
/unsubscribe [SYMBOL] [TYPE] - إلغاء الاشتراك

**⚙️ تعديل الإعدادات:**
/threshold [TYPE] [VALUE] - تعديل الحدود

**📝 أمثلة عملية:**
`/subscribe BTC/USDT crypto`
`/subscribe EUR/USD forex`
`/threshold volume 3.5`
`/threshold price 7.0`

**🚨 معايير التنبيه:**
• زيادة الحجم 3 أضعاف أو أكثر
• تغيير السعر 5% أو أكثر خلال 5 دقائق

**💡 نصائح:**
• استخدم الأزرار للتنقل السريع
• أرسل الأوامر كرسائل نصية منفصلة
• البوت يعمل 24/7 تلقائياً
        """
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="menu")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )
    
    async def toggle_monitoring(self, query):
        """Toggle monitoring on/off"""
        self.monitoring_active = not self.monitoring_active
        
        if self.monitoring_active:
            message = "▶️ **تم تشغيل المراقبة**\n\nالبوت يعمل الآن ويراقب الأسواق. ستصلك التنبيهات عند ضخ السيولة."
        else:
            message = "🛑 **تم إيقاف المراقبة**\n\nالبوت متوقف مؤقتاً. لن يرسل تنبيهات حتى تعيد تشغيله."
        
        keyboard = [
            [InlineKeyboardButton("🛑 إيقاف" if self.monitoring_active else "▶️ تشغيل", 
                                callback_data="toggle_monitoring")],
            [InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="menu")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )
    
    async def refresh_menu(self, query):
        """Refresh the menu"""
        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text="🔄 **جاري التحديث...**"
        )
        await asyncio.sleep(1)
        await self.back_to_menu(query)
    
    async def back_to_menu(self, query):
        """Go back to main menu"""
        status_emoji = "🟢" if self.monitoring_active else "🔴"
        
        keyboard = [
            [
                InlineKeyboardButton("📊 حالة البوت", callback_data="status"),
                InlineKeyboardButton("📈 إحصائياتي", callback_data="mystats")
            ],
            [
                InlineKeyboardButton("🔔 الاشتراكات", callback_data="subscriptions"),
                InlineKeyboardButton("⚙️ الإعدادات", callback_data="settings")
            ],
            [
                InlineKeyboardButton("🛑 إيقاف" if self.monitoring_active else "▶️ تشغيل", 
                                   callback_data="toggle_monitoring")
            ],
            [
                InlineKeyboardButton("❓ المساعدة", callback_data="help"),
                InlineKeyboardButton("🔄 تحديث", callback_data="refresh")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        message = f"""
🚀 **بوت مراقبة ضخ السيولة**

{status_emoji} **الحالة:** {'يعمل' if self.monitoring_active else 'متوقف'}
📊 **يراقب:** 10 عملات رقمية + 10 فوركس
🔔 **الأزرار تعمل بشكل كامل!**

⏰ **الوقت:** {datetime.now().strftime('%H:%M:%S')}
        """
        
        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

    async def handle_subscribe(self, message):
        """Handle subscribe command"""
        parts = message.text.split()
        user_id = str(message.from_user.id)

        if len(parts) < 3:
            response = "❌ **صيغة خاطئة**\n\n**الصيغة الصحيحة:**\n`/subscribe BTC/USDT crypto`"
            await self.bot.send_message(message.chat_id, response, parse_mode='Markdown')
            return

        symbol = parts[1].upper()
        market_type = parts[2].lower()

        if market_type not in ['crypto', 'forex']:
            await self.bot.send_message(message.chat_id, "❌ نوع السوق يجب أن يكون crypto أو forex")
            return

        success = self.user_settings.add_user_subscription(user_id, symbol, market_type)

        if success:
            emoji = "🪙" if market_type == "crypto" else "💱"
            response = f"✅ **تم الاشتراك بنجاح!**\n\n{emoji} {symbol} ({market_type})"
        else:
            response = f"❌ **فشل في الاشتراك في {symbol}**"

        keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="menu")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await self.bot.send_message(message.chat_id, response, parse_mode='Markdown', reply_markup=reply_markup)

    async def handle_unsubscribe(self, message):
        """Handle unsubscribe command"""
        parts = message.text.split()
        user_id = str(message.from_user.id)

        if len(parts) < 3:
            response = "❌ **صيغة خاطئة**\n\n**الصيغة الصحيحة:**\n`/unsubscribe BTC/USDT crypto`"
            await self.bot.send_message(message.chat_id, response, parse_mode='Markdown')
            return

        symbol = parts[1].upper()
        market_type = parts[2].lower()

        success = self.user_settings.remove_user_subscription(user_id, symbol, market_type)

        if success:
            response = f"✅ **تم إلغاء الاشتراك في {symbol} بنجاح!**"
        else:
            response = f"❌ **فشل في إلغاء الاشتراك**"

        keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="menu")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await self.bot.send_message(message.chat_id, response, parse_mode='Markdown', reply_markup=reply_markup)

    async def handle_threshold(self, message):
        """Handle threshold command"""
        parts = message.text.split()
        user_id = str(message.from_user.id)

        if len(parts) < 3:
            response = "❌ **صيغة خاطئة**\n\n**الصيغة الصحيحة:**\n`/threshold volume 3.5`"
            await self.bot.send_message(message.chat_id, response, parse_mode='Markdown')
            return

        threshold_type = parts[1].lower()
        try:
            value = float(parts[2])
        except ValueError:
            await self.bot.send_message(message.chat_id, "❌ القيمة يجب أن تكون رقماً")
            return

        if threshold_type == 'volume':
            success = self.user_settings.update_user_preference(user_id, 'notifications.volume_threshold', value)
            setting_name = "حد الحجم"
        elif threshold_type == 'price':
            success = self.user_settings.update_user_preference(user_id, 'notifications.price_threshold', value)
            setting_name = "حد السعر"
        elif threshold_type == 'confidence':
            if not 0 <= value <= 1:
                await self.bot.send_message(message.chat_id, "❌ حد الثقة يجب أن يكون بين 0 و 1")
                return
            success = self.user_settings.update_user_preference(user_id, 'notifications.min_confidence', value)
            setting_name = "حد الثقة"
        else:
            await self.bot.send_message(message.chat_id, "❌ نوع الحد غير صحيح. استخدم: volume, price, أو confidence")
            return

        if success:
            response = f"✅ **تم تحديث {setting_name} إلى {value} بنجاح!**"
        else:
            response = f"❌ **فشل في تحديث {setting_name}**"

        keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="menu")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await self.bot.send_message(message.chat_id, response, parse_mode='Markdown', reply_markup=reply_markup)

    async def send_pump_alert(self, pump_data):
        """Send pump alert with interactive buttons"""
        try:
            symbol = pump_data.get('symbol', 'Unknown')
            market_type = pump_data.get('market_type', 'Unknown')
            price_change = pump_data.get('price_change_percent', 0)
            volume_spike = pump_data.get('volume_spike', 0)
            current_price = pump_data.get('current_price', 0)

            trend_emoji = "🚀" if price_change > 0 else "📉"
            market_emoji = "🪙" if market_type == "crypto" else "💱"

            message = f"""
{trend_emoji} **تنبيه ضخ سيولة!** {market_emoji}

**الرمز:** {symbol}
**السوق:** {market_type.upper()}
**السعر:** {current_price:.6f}
**التغيير:** {price_change:+.2f}%
**الحجم:** {volume_spike:.1f}x

⏰ {datetime.now().strftime('%H:%M:%S')}

#ضخ_سيولة #{symbol.replace('/', '_')}
            """

            keyboard = [
                [InlineKeyboardButton("📊 تفاصيل", callback_data=f"details_{symbol}")],
                [InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            if self.chat_id:
                await self.bot.send_message(
                    chat_id=self.chat_id,
                    text=message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                logger.info(f"📤 Interactive alert sent for {symbol}")

        except Exception as e:
            logger.error(f"❌ Failed to send alert: {e}")

    async def send_startup_message(self):
        """Send startup message"""
        try:
            message = """
🚀 **بوت مراقبة ضخ السيولة**

✅ البوت يعمل بكامل طاقته!
🎛️ جميع الأزرار تعمل الآن بشكل مثالي!
📊 تحكم شامل في جميع الإعدادات
🔔 تنبيهات تفاعلية عند ضخ السيولة

**تم حل مشكلة المنطقة الزمنية نهائياً!**
**بدون Application Framework - يعمل بشكل مثالي!**

**اضغط الزر للبدء:**
            """

            keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="menu")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            if self.chat_id:
                await self.bot.send_message(
                    chat_id=self.chat_id,
                    text=message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                logger.info("✅ Interactive startup message sent")
        except Exception as e:
            logger.error(f"❌ Failed to send startup message: {e}")

async def run_monitoring_loop(bot):
    """Enhanced monitoring loop with full control"""
    logger.info("🔄 Starting simple monitoring loop")

    while True:
        try:
            # Process Telegram updates
            await bot.process_updates()

            # Only monitor if active
            if bot.monitoring_active:
                # Get market data
                crypto_data = await bot.market_monitor.get_crypto_data()
                forex_data = await bot.market_monitor.get_forex_data()

                # Detect pumps
                crypto_pumps = bot.pump_detector.detect_crypto_pumps(crypto_data)
                forex_pumps = bot.pump_detector.detect_forex_pumps(forex_data)

                # Send alerts
                for pump in crypto_pumps + forex_pumps:
                    if bot.chat_id:
                        user_id = bot.chat_id
                        if bot.user_settings.should_send_alert(user_id, pump):
                            await bot.send_pump_alert(pump)
                            bot.user_settings.record_alert_sent(user_id, pump, "Interactive alert")
                            bot.pump_detector.record_pump_alert(pump)

                logger.info(f"✅ Monitoring cycle completed - Active: {bot.monitoring_active}")
                await asyncio.sleep(Config.CHECK_INTERVAL_SECONDS)
            else:
                # If monitoring is stopped, just wait
                logger.info("⏸️ Monitoring paused")
                await asyncio.sleep(5)

        except Exception as e:
            logger.error(f"❌ Error in monitoring loop: {e}")
            await asyncio.sleep(10)

async def main():
    """Main function to run the simple working bot"""
    try:
        # Setup logging
        await setup_logging()

        logger.info("🚀 Starting Simple Working Interactive Bot")
        logger.info("🔧 NO Application Framework - NO Timezone Issues!")

        # Initialize bot
        bot = SimpleWorkingBot()

        # Send startup message
        await bot.send_startup_message()

        logger.info("✅ Simple working interactive bot started successfully")
        logger.info("🎛️ ALL BUTTONS ARE NOW WORKING!")
        logger.info("🌍 NO TIMEZONE ISSUES!")

        # Start monitoring
        await run_monitoring_loop(bot)

    except KeyboardInterrupt:
        logger.info("🛑 Bot stopped by user")
    except Exception as e:
        logger.error(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
