"""
Telegram Bot module for sending liquidity pump alerts
"""
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from telegram import Bo<PERSON>, Update
from telegram.ext import Application, CommandHandler, ContextTypes
import pytz
from loguru import logger

from config import Config
from .user_settings import UserSettingsManager

class TelegramBot:
    """Telegram bot for sending pump alerts"""
    
    def __init__(self):
        self.bot_token = Config.TELEGRAM_BOT_TOKEN
        self.chat_id = Config.TELEGRAM_CHAT_ID
        self.bot = None
        self.application = None
        self.user_settings = UserSettingsManager()
        self._initialize_bot()
    
    def _initialize_bot(self):
        """Initialize the Telegram bot"""
        try:
            if not self.bot_token:
                raise ValueError("TELEGRAM_BOT_TOKEN is required")
            
            self.bot = Bot(token=self.bot_token)
            # Set timezone to UTC to avoid pytz issues
            self.application = (Application.builder()
                              .token(self.bot_token)
                              .job_queue(None)  # Disable job queue to avoid timezone issues
                              .build())
            
            # Add command handlers
            self.application.add_handler(CommandHandler("start", self.start_command))
            self.application.add_handler(CommandHandler("help", self.help_command))
            self.application.add_handler(CommandHandler("status", self.status_command))
            self.application.add_handler(CommandHandler("settings", self.settings_command))
            self.application.add_handler(CommandHandler("mystats", self.mystats_command))
            self.application.add_handler(CommandHandler("subscribe", self.subscribe_command))
            self.application.add_handler(CommandHandler("unsubscribe", self.unsubscribe_command))
            self.application.add_handler(CommandHandler("threshold", self.threshold_command))
            
            logger.info("✅ Telegram bot initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Telegram bot: {e}")
            raise
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        welcome_message = """
🚀 **مرحباً بك في بوت مراقبة ضخ السيولة!**

هذا البوت يراقب العملات الرقمية والفوركس ويرسل تنبيهات فورية عند اكتشاف ضخ سيولة.

**الأوامر المتاحة:**
/help - عرض المساعدة
/status - حالة البوت
/settings - الإعدادات

**المميزات:**
📊 مراقبة العملات الرقمية الرئيسية
💱 مراقبة أزواج الفوركس
🔔 تنبيهات فورية عند ضخ السيولة
📈 تحليل الحجم والسعر

البوت يعمل الآن! ستصلك التنبيهات تلقائياً.
        """
        await update.message.reply_text(welcome_message, parse_mode='Markdown')
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        help_message = """
📖 **دليل استخدام البوت**

**ما يراقبه البوت:**
• العملات الرقمية: BTC, ETH, BNB, ADA, SOL, XRP وغيرها
• الفوركس: EUR/USD, GBP/USD, USD/JPY وغيرها

**معايير التنبيه:**
• زيادة الحجم بـ 3 أضعاف أو أكثر
• تغيير السعر بـ 5% أو أكثر خلال 5 دقائق

**الأوامر:**
/start - بدء البوت
/status - حالة البوت والإحصائيات
/settings - عرض الإعدادات
/mystats - إحصائياتك الشخصية
/subscribe - الاشتراك في رمز معين
/unsubscribe - إلغاء الاشتراك في رمز
/threshold - تعديل حدود التنبيه

**ملاحظة:** البوت يعمل 24/7 ويرسل التنبيهات تلقائياً
        """
        await update.message.reply_text(help_message, parse_mode='Markdown')
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command"""
        status_message = f"""
📊 **حالة البوت**

🟢 **الحالة:** يعمل
⏰ **وقت التحديث:** كل {Config.CHECK_INTERVAL_SECONDS} ثانية
📈 **العملات المراقبة:** {len(Config.CRYPTO_PAIRS)} عملة رقمية
💱 **أزواج الفوركس:** {len(Config.FOREX_PAIRS)} زوج

**آخر فحص:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**الإعدادات الحالية:**
• حد الحجم: {Config.ALERT_THRESHOLDS['volume_spike']}x
• حد السعر: {Config.ALERT_THRESHOLDS['price_change']}%
• النافذة الزمنية: {Config.ALERT_THRESHOLDS['time_window']} ثانية
        """
        await update.message.reply_text(status_message, parse_mode='Markdown')
    
    async def settings_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /settings command"""
        settings_message = """
⚙️ **إعدادات البوت**

**الإعدادات الحالية:**
• مراقبة العملات الرقمية: ✅
• مراقبة الفوركس: ✅
• التنبيهات الفورية: ✅

**لتخصيص الإعدادات، تواصل مع المطور**

**العملات المراقبة:**
🪙 العملات الرقمية: BTC, ETH, BNB, ADA, SOL, XRP, DOT, DOGE, AVAX, MATIC
💱 الفوركس: EUR/USD, GBP/USD, USD/JPY, USD/CHF, AUD/USD, USD/CAD, NZD/USD, EUR/GBP, EUR/JPY, GBP/JPY
        """
        await update.message.reply_text(settings_message, parse_mode='Markdown')
    
    async def send_pump_alert(self, pump_data: Dict[str, Any]):
        """Send a pump alert to the configured chat"""
        try:
            message = self._format_pump_message(pump_data)
            
            if self.chat_id:
                await self.bot.send_message(
                    chat_id=self.chat_id,
                    text=message,
                    parse_mode='Markdown'
                )
                logger.info(f"📤 Pump alert sent for {pump_data.get('symbol', 'Unknown')}")
            else:
                logger.warning("⚠️ No chat ID configured, cannot send alert")
                
        except Exception as e:
            logger.error(f"❌ Failed to send pump alert: {e}")
    
    def _format_pump_message(self, pump_data: Dict[str, Any]) -> str:
        """Format pump data into a readable message"""
        symbol = pump_data.get('symbol', 'Unknown')
        market_type = pump_data.get('market_type', 'Unknown')
        price_change = pump_data.get('price_change_percent', 0)
        volume_spike = pump_data.get('volume_spike', 0)
        current_price = pump_data.get('current_price', 0)
        volume = pump_data.get('volume', 0)
        timestamp = pump_data.get('timestamp', datetime.now())
        
        # Determine emoji based on price change
        trend_emoji = "🚀" if price_change > 0 else "📉"
        market_emoji = "🪙" if market_type == "crypto" else "💱"
        
        message = f"""
{trend_emoji} **تنبيه ضخ سيولة!** {market_emoji}

**الرمز:** {symbol}
**السوق:** {market_type.upper()}
**السعر الحالي:** {current_price:.6f}
**تغيير السعر:** {price_change:+.2f}%
**زيادة الحجم:** {volume_spike:.1f}x
**الحجم:** {volume:,.0f}

⏰ **الوقت:** {timestamp.strftime('%H:%M:%S')}
📅 **التاريخ:** {timestamp.strftime('%Y-%m-%d')}

#ضخ_سيولة #{symbol.replace('/', '_')} #{market_type}
        """
        
        return message.strip()
    
    async def send_test_message(self, message: str = "🧪 رسالة تجريبية من البوت"):
        """Send a test message"""
        try:
            if self.chat_id:
                await self.bot.send_message(
                    chat_id=self.chat_id,
                    text=message,
                    parse_mode='Markdown'
                )
                logger.info("✅ Test message sent successfully")
                return True
            else:
                logger.warning("⚠️ No chat ID configured")
                return False
        except Exception as e:
            logger.error(f"❌ Failed to send test message: {e}")
            return False

    async def mystats_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /mystats command"""
        user_id = str(update.effective_user.id)
        stats = self.user_settings.get_user_stats(user_id)

        stats_message = f"""
📊 **إحصائياتك الشخصية**

👤 **معرف المستخدم:** {stats['user_id']}
🔔 **إجمالي التنبيهات:** {stats['total_alerts']}
📈 **الاشتراكات النشطة:** {stats['active_subscriptions']}
⏰ **تنبيهات آخر 24 ساعة:** {stats['recent_alerts_24h']}

📅 **تاريخ اليوم:** {datetime.now().strftime('%Y-%m-%d')}
        """
        await update.message.reply_text(stats_message, parse_mode='Markdown')

    async def subscribe_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /subscribe command"""
        user_id = str(update.effective_user.id)

        if not context.args:
            help_message = """
📝 **كيفية الاشتراك في رمز:**

**للعملات الرقمية:**
`/subscribe BTC/USDT crypto`
`/subscribe ETH/USDT crypto`

**للفوركس:**
`/subscribe EUR/USD forex`
`/subscribe GBP/USD forex`

**الرموز المتاحة:**
🪙 العملات الرقمية: BTC/USDT, ETH/USDT, BNB/USDT, ADA/USDT, SOL/USDT
💱 الفوركس: EUR/USD, GBP/USD, USD/JPY, USD/CHF, AUD/USD
            """
            await update.message.reply_text(help_message, parse_mode='Markdown')
            return

        if len(context.args) < 2:
            await update.message.reply_text("❌ يرجى تحديد الرمز ونوع السوق\nمثال: `/subscribe BTC/USDT crypto`", parse_mode='Markdown')
            return

        symbol = context.args[0].upper()
        market_type = context.args[1].lower()

        if market_type not in ['crypto', 'forex']:
            await update.message.reply_text("❌ نوع السوق يجب أن يكون `crypto` أو `forex`", parse_mode='Markdown')
            return

        success = self.user_settings.add_user_subscription(user_id, symbol, market_type)

        if success:
            await update.message.reply_text(f"✅ تم الاشتراك في {symbol} ({market_type})")
        else:
            await update.message.reply_text(f"❌ فشل في الاشتراك في {symbol}")

    async def unsubscribe_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /unsubscribe command"""
        user_id = str(update.effective_user.id)

        if not context.args:
            # Show current subscriptions
            subscriptions = self.user_settings.get_user_subscriptions(user_id)

            if not subscriptions:
                await update.message.reply_text("📭 لا توجد اشتراكات نشطة")
                return

            message = "📋 **اشتراكاتك الحالية:**\n\n"
            for sub in subscriptions:
                message += f"• {sub['symbol']} ({sub['market_type']})\n"

            message += "\n💡 لإلغاء الاشتراك: `/unsubscribe SYMBOL MARKET_TYPE`"
            await update.message.reply_text(message, parse_mode='Markdown')
            return

        if len(context.args) < 2:
            await update.message.reply_text("❌ يرجى تحديد الرمز ونوع السوق\nمثال: `/unsubscribe BTC/USDT crypto`", parse_mode='Markdown')
            return

        symbol = context.args[0].upper()
        market_type = context.args[1].lower()

        success = self.user_settings.remove_user_subscription(user_id, symbol, market_type)

        if success:
            await update.message.reply_text(f"✅ تم إلغاء الاشتراك في {symbol} ({market_type})")
        else:
            await update.message.reply_text(f"❌ فشل في إلغاء الاشتراك في {symbol}")

    async def threshold_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /threshold command"""
        user_id = str(update.effective_user.id)

        if not context.args:
            settings = self.user_settings.get_user_settings(user_id)
            current_message = f"""
⚙️ **حدود التنبيه الحالية:**

📊 **حد الحجم:** {settings['notifications']['volume_threshold']}x
📈 **حد السعر:** {settings['notifications']['price_threshold']}%
🎯 **الثقة الدنيا:** {settings['notifications']['min_confidence']}

**لتعديل الحدود:**
`/threshold volume 3.5` - تعديل حد الحجم
`/threshold price 7.0` - تعديل حد السعر
`/threshold confidence 0.8` - تعديل حد الثقة
            """
            await update.message.reply_text(current_message, parse_mode='Markdown')
            return

        if len(context.args) < 2:
            await update.message.reply_text("❌ يرجى تحديد نوع الحد والقيمة\nمثال: `/threshold volume 3.5`", parse_mode='Markdown')
            return

        threshold_type = context.args[0].lower()
        try:
            value = float(context.args[1])
        except ValueError:
            await update.message.reply_text("❌ القيمة يجب أن تكون رقماً", parse_mode='Markdown')
            return

        if threshold_type == 'volume':
            success = self.user_settings.update_user_preference(user_id, 'notifications.volume_threshold', value)
            setting_name = "حد الحجم"
        elif threshold_type == 'price':
            success = self.user_settings.update_user_preference(user_id, 'notifications.price_threshold', value)
            setting_name = "حد السعر"
        elif threshold_type == 'confidence':
            if not 0 <= value <= 1:
                await update.message.reply_text("❌ حد الثقة يجب أن يكون بين 0 و 1", parse_mode='Markdown')
                return
            success = self.user_settings.update_user_preference(user_id, 'notifications.min_confidence', value)
            setting_name = "حد الثقة"
        else:
            await update.message.reply_text("❌ نوع الحد غير صحيح. استخدم: volume, price, أو confidence", parse_mode='Markdown')
            return

        if success:
            await update.message.reply_text(f"✅ تم تحديث {setting_name} إلى {value}")
        else:
            await update.message.reply_text(f"❌ فشل في تحديث {setting_name}")
