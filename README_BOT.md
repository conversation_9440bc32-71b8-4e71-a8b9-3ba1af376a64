# 🚀 بوت مراقبة ضخ السيولة مع أزرار تفاعلية

## 📋 وصف المشروع

بوت تيليجرام متقدم لمراقبة ضخ السيولة في العملات الرقمية والفوركس مع نظام إدارة اشتراكات شامل وأزرار تفاعلية.

## ✨ المميزات الرئيسية

### 🎛️ أزرار تفاعلية كاملة
- قائمة رئيسية تفاعلية
- أزرار للتنقل السريع
- واجهة سهلة الاستخدام

### 👥 نظام إدارة الاشتراكات
- تحكم كامل في المستخدمين المشتركين
- رسائل اشتراك مخصصة
- إدارة متقدمة للمدير

### 📊 مراقبة شاملة
- **10 عملات رقمية:** BTC/USDT, ETH/USDT, BNB/USDT, ADA/USDT, SOL/USDT, XRP/USDT, DOT/USDT, DOGE/USDT, AVAX/USDT, MATIC/USDT
- **10 أزواج فوركس:** EUR/USD, GBP/USD, USD/JPY, USD/CHF, AUD/USD, USD/CAD, NZD/USD, EUR/GBP, EUR/JPY, GBP/JPY

### ⚙️ إعدادات قابلة للتخصيص
- حدود الحجم والسعر
- تشغيل/إيقاف التنبيهات
- اشتراكات فردية في العملات

## 🛠️ التثبيت والإعداد

### 1. متطلبات النظام
```bash
pip install python-telegram-bot asyncio
```

### 2. إعداد البوت
1. افتح ملف `bot_complete.py`
2. عدّل الإعدادات التالية:

```python
# ===== إعدادات البوت الأساسية =====
BOT_TOKEN = "ضع_توكن_البوت_هنا"        # توكن البوت من BotFather
CHAT_ID = "ضع_معرف_المجموعة_هنا"       # معرف المجموعة أو القناة
ADMIN_ID = "772419417"                  # أسامة التبعي - معرف المدير
```

### 3. تشغيل البوت
```bash
python bot_complete.py
```

## 📱 كيفية الاستخدام

### للمستخدمين العاديين

#### 🔐 الاشتراك في البوت
للحصول على إذن استخدام البوت، تواصل مع:
- **أسامة التبعي**
- **Telegram ID:** 772419417
- **رقم الهاتف:** 772419417

#### 🎛️ الأوامر الأساسية
- `/start` - بدء البوت وعرض القائمة الرئيسية
- `/menu` - عرض القائمة الرئيسية

#### 🔔 إدارة الاشتراكات في العملات
```
/subscribe_crypto BTC/USDT    # الاشتراك في عملة رقمية
/subscribe_forex EUR/USD     # الاشتراك في زوج فوركس
/unsubscribe BTC/USDT        # إلغاء الاشتراك
```

#### ⚙️ تعديل الإعدادات
```
/set_volume 4.0              # تعديل حد الحجم إلى 4 أضعاف
/set_price 7.0               # تعديل حد السعر إلى 7%
/toggle_notifications        # تشغيل/إيقاف التنبيهات
```

### للمدير (أسامة التبعي)

#### 👥 إدارة المستخدمين
```
/add_user USER_ID            # إضافة مستخدم جديد
/remove_user USER_ID         # إزالة مستخدم
/list_users                  # عرض قائمة المستخدمين
```

#### 🎛️ أزرار المدير الخاصة
- **👥 إدارة المستخدمين** - عرض وإدارة المشتركين
- **📊 إحصائيات عامة** - عرض إحصائيات البوت
- **🛑 إيقاف/تشغيل البوت** - التحكم في حالة المراقبة

## 📊 الأزرار التفاعلية

### 🎛️ القائمة الرئيسية
- **📊 حالة البوت** - عرض حالة البوت التفصيلية
- **📈 إحصائياتي** - عرض إحصائيات المستخدم الشخصية
- **🔔 إدارة الاشتراكات** - إدارة اشتراكات العملات
- **⚙️ الإعدادات** - تعديل إعدادات التنبيهات
- **❓ المساعدة** - دليل الاستخدام الشامل
- **🔄 تحديث** - تحديث القائمة

### 👑 أزرار المدير الإضافية
- **👥 إدارة المستخدمين** - إضافة وإزالة المستخدمين
- **📊 إحصائيات عامة** - إحصائيات شاملة للبوت
- **🛑 إيقاف/▶️ تشغيل البوت** - التحكم في المراقبة

## 💾 قاعدة البيانات

البوت يستخدم ملف JSON بسيط لحفظ البيانات:
- `users_data.json` - بيانات المستخدمين والإعدادات

### هيكل البيانات:
```json
{
  "subscribed_users": ["USER_ID1", "USER_ID2"],
  "user_settings": {
    "USER_ID": {
      "volume_threshold": 3.0,
      "price_threshold": 5.0,
      "notifications_enabled": true
    }
  },
  "user_subscriptions": {
    "USER_ID": ["BTC/USDT", "EUR/USD"]
  }
}
```

## 🔧 التخصيص والتطوير

### إضافة عملات جديدة
عدّل القوائم في الكلاس:
```python
self.crypto_pairs = [
    "BTC/USDT", "ETH/USDT", "عملة_جديدة/USDT"
]

self.forex_pairs = [
    "EUR/USD", "GBP/USD", "زوج_جديد"
]
```

### تعديل رسائل الاشتراك
عدّل دالة `send_subscription_required_message()` لتخصيص رسالة الاشتراك.

## 🚨 معايير التنبيه

- **حد الحجم:** زيادة الحجم بالأضعاف المحددة
- **حد السعر:** تغيير السعر بالنسبة المئوية المحددة
- **التنبيهات الفورية:** عند تحقق الشروط

## 📞 الدعم والتواصل

للحصول على الدعم أو الاشتراك في البوت:

**أسامة التبعي**
- **Telegram:** @772419417
- **ID:** 772419417
- **الهاتف:** 772419417

## 📝 ملاحظات مهمة

1. **الأمان:** البوت يتطلب اشتراك مسبق للاستخدام
2. **الأداء:** البوت يعمل 24/7 بشكل تلقائي
3. **التحديثات:** جميع الأزرار تعمل بشكل تفاعلي
4. **البيانات:** يتم حفظ جميع الإعدادات تلقائياً

## 🎯 الخلاصة

بوت متكامل وجاهز للاستخدام مع:
- ✅ أزرار تفاعلية كاملة
- ✅ نظام إدارة اشتراكات متقدم
- ✅ مراقبة شاملة للأسواق
- ✅ إعدادات قابلة للتخصيص
- ✅ واجهة سهلة الاستخدام

**للاشتراك تواصل مع أسامة التبعي: 772419417**
