# 🚀 بوت تيليجرام لمراقبة ضخ السيولة

بوت ذكي لمراقبة العملات الرقمية والفوركس وإرسال تنبيهات فورية عند اكتشاف ضخ السيولة.

## ✨ المميزات

- 🪙 **مراقبة العملات الرقمية**: مراقبة العملات الرئيسية مثل BTC, ETH, BNB وغيرها
- 💱 **مراقبة الفوركس**: مراقبة أزواج العملات الرئيسية مثل EUR/USD, GBP/USD
- 🔔 **تنبيهات فورية**: إرسال تنبيهات عبر تيليجرام عند اكتشاف ضخ السيولة
- 📊 **تحليل ذكي**: خوارزميات متقدمة لكشف أنماط ضخ السيولة
- ⚙️ **قابل للتخصيص**: إعدادات قابلة للتخصيص لحدود التنبيه
- 🗄️ **قاعدة بيانات**: تخزين البيانات التاريخية لتحسين دقة التنبيهات

## 🛠️ التثبيت والإعداد

### 1. متطلبات النظام

- Python 3.8 أو أحدث
- حساب تيليجرام وبوت
- مفاتيح API (اختيارية لحدود أعلى)

### 2. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 3. إعداد البيئة

1. انسخ ملف `.env.example` إلى `.env`:
```bash
copy .env.example .env
```

2. املأ المتغيرات المطلوبة في ملف `.env`:

```env
# مطلوب
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here

# اختياري (لحدود أعلى)
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key
```

### 4. إنشاء بوت تيليجرام

1. تحدث مع [@BotFather](https://t.me/botfather) على تيليجرام
2. أرسل `/newbot` واتبع التعليمات
3. احصل على التوكن وضعه في `TELEGRAM_BOT_TOKEN`
4. للحصول على Chat ID، أرسل رسالة للبوت ثم زر:
   ```
   https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates
   ```

## 🚀 تشغيل البوت

```bash
python main.py
```

## 📊 معايير التنبيه

### العملات الرقمية
- **زيادة الحجم**: 3 أضعاف الحجم العادي أو أكثر
- **تغيير السعر**: 5% أو أكثر خلال فترة قصيرة
- **النافذة الزمنية**: 5 دقائق

### الفوركس
- **زيادة الحجم**: 1.5 ضعف الحجم العادي أو أكثر
- **تغيير السعر**: 2.5% أو أكثر خلال فترة قصيرة
- **النافذة الزمنية**: 5 دقائق

## 🔧 التخصيص

يمكنك تخصيص الإعدادات في ملف `config.py`:

```python
# حدود التنبيه
ALERT_THRESHOLDS = {
    'volume_spike': 3.0,  # 3x الحجم العادي
    'price_change': 5.0,  # 5% تغيير السعر
    'time_window': 300,   # 5 دقائق
}

# العملات المراقبة
CRYPTO_PAIRS = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', ...]
FOREX_PAIRS = ['EUR/USD', 'GBP/USD', 'USD/JPY', ...]
```

## 📱 أوامر البوت

- `/start` - بدء البوت وعرض الترحيب
- `/help` - عرض المساعدة والتعليمات
- `/status` - عرض حالة البوت والإحصائيات
- `/settings` - عرض الإعدادات الحالية

## 📁 هيكل المشروع

```
bot_trading/
├── main.py                 # نقطة البداية الرئيسية
├── config.py              # إعدادات البوت
├── requirements.txt       # المتطلبات
├── .env.example          # مثال على متغيرات البيئة
├── README.md             # هذا الملف
├── src/
│   ├── __init__.py
│   ├── telegram_bot.py   # وحدة بوت تيليجرام
│   ├── market_monitor.py # مراقبة الأسواق
│   └── pump_detector.py  # كشف ضخ السيولة
├── data/                 # قاعدة البيانات
└── logs/                 # ملفات السجل
```

## 🔗 مصادر البيانات

- **Binance API**: للعملات الرقمية (مجاني مع حدود)
- **CoinGecko API**: بديل للعملات الرقمية
- **Alpha Vantage**: للفوركس (مجاني مع حدود)
- **Yahoo Finance**: بديل للفوركس

## ⚠️ تنبيهات مهمة

- احتفظ بمفاتيح API آمنة ولا تشاركها
- ابدأ بحسابات تجريبية قبل الاستخدام الفعلي
- راقب استهلاك API لتجنب تجاوز الحدود
- البوت للأغراض التعليمية والإعلامية فقط

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك أسئلة، يرجى فتح Issue في GitHub.

---

**تنبيه**: هذا البوت للأغراض التعليمية والإعلامية فقط. لا يُعتبر نصيحة استثمارية.
