@echo off
echo 🛠️ Setting up Liquidity Pump Detection Bot...
echo =============================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found
python --version

REM Run setup script
echo.
echo 📦 Running setup...
python setup.py

echo.
echo ✅ Setup completed!
echo.
echo 📋 Next steps:
echo 1. Edit .env file with your API keys
echo 2. Run: run_bot.bat
echo.
pause
