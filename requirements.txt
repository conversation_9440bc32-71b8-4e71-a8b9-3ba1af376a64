# Telegram Bot Dependencies
python-telegram-bot==20.7
requests==2.31.0
aiohttp==3.9.1

# Data Analysis and Processing
pandas==2.1.4
numpy==1.24.3

# API Clients
ccxt==4.1.92  # Cryptocurrency exchange APIs
yfinance==0.2.28  # Yahoo Finance for forex data

# Configuration and Environment
python-dotenv==1.0.0
pyyaml==6.0.1

# Database (optional for storing user preferences)
sqlite3  # Built-in Python module

# Logging and Monitoring
loguru==0.7.2

# Async support
asyncio  # Built-in Python module
aiofiles==23.2.1

# Mathematical calculations
scipy==1.11.4
