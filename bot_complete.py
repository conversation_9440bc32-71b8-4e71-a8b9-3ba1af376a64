"""
بوت تيليجرام لمراقبة ضخ السيولة مع أزرار تفاعلية وإدارة الاشتراكات
Bot Telegram for Liquidity Pump Monitoring with Interactive Buttons and Subscription Management
"""

import asyncio
import json
import os
from datetime import datetime
from telegram import Bot, InlineKeyboardButton, InlineKeyboardMarkup

# ===== إعدادات البوت الأساسية =====
# Basic Bot Configuration
BOT_TOKEN = "7729526066:AAGJhJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ"  # ضع توكن البوت هنا
CHAT_ID = "-1002345678901"  # ضع معرف المجموعة هنا
ADMIN_ID = "6434356070"  # أسامة التبعي - معرف المدير

# ===== قاعدة بيانات بسيطة للمستخدمين =====
# Simple Database for Users
USERS_FILE = "users_data.json"

def load_users_data():
    """تحميل بيانات المستخدمين من الملف"""
    try:
        if os.path.exists(USERS_FILE):
            with open(USERS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {
            "subscribed_users": [],  # قائمة المستخدمين المشتركين
            "user_settings": {},     # إعدادات كل مستخدم
            "user_subscriptions": {} # اشتراكات كل مستخدم في العملات
        }
    except:
        return {"subscribed_users": [], "user_settings": {}, "user_subscriptions": {}}

def save_users_data(data):
    """حفظ بيانات المستخدمين في الملف"""
    try:
        with open(USERS_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"خطأ في حفظ البيانات: {e}")

# تحميل البيانات عند بدء البوت
users_data = load_users_data()

class LiquidityPumpBot:
    """كلاس البوت الرئيسي لمراقبة ضخ السيولة"""
    
    def __init__(self):
        """تهيئة البوت وإعداد المتغيرات الأساسية"""
        self.bot = Bot(token=BOT_TOKEN)
        self.monitoring_active = True  # حالة المراقبة (تشغيل/إيقاف)
        self.last_update_id = 0       # آخر معرف تحديث تم معالجته
        
        # قائمة العملات الرقمية المراقبة
        self.crypto_pairs = [
            "BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT",
            "XRP/USDT", "DOT/USDT", "DOGE/USDT", "AVAX/USDT", "MATIC/USDT"
        ]
        
        # قائمة أزواج الفوركس المراقبة
        self.forex_pairs = [
            "EUR/USD", "GBP/USD", "USD/JPY", "USD/CHF", "AUD/USD",
            "USD/CAD", "NZD/USD", "EUR/GBP", "EUR/JPY", "GBP/JPY"
        ]
        
        print("✅ تم تهيئة البوت بنجاح")
    
    def is_user_subscribed(self, user_id):
        """فحص ما إذا كان المستخدم مشترك أم لا"""
        return str(user_id) in users_data["subscribed_users"]
    
    def is_admin(self, user_id):
        """فحص ما إذا كان المستخدم مدير أم لا"""
        return str(user_id) == ADMIN_ID
    
    def add_user_subscription(self, user_id):
        """إضافة مستخدم جديد للاشتراك"""
        user_id = str(user_id)
        if user_id not in users_data["subscribed_users"]:
            users_data["subscribed_users"].append(user_id)
            # إعدادات افتراضية للمستخدم الجديد
            users_data["user_settings"][user_id] = {
                "volume_threshold": 3.0,    # حد الحجم
                "price_threshold": 5.0,     # حد السعر
                "notifications_enabled": True
            }
            users_data["user_subscriptions"][user_id] = []
            save_users_data(users_data)
            return True
        return False
    
    def remove_user_subscription(self, user_id):
        """إزالة مستخدم من الاشتراك"""
        user_id = str(user_id)
        if user_id in users_data["subscribed_users"]:
            users_data["subscribed_users"].remove(user_id)
            if user_id in users_data["user_settings"]:
                del users_data["user_settings"][user_id]
            if user_id in users_data["user_subscriptions"]:
                del users_data["user_subscriptions"][user_id]
            save_users_data(users_data)
            return True
        return False
    
    async def send_subscription_required_message(self, chat_id):
        """إرسال رسالة طلب الاشتراك للمستخدمين غير المشتركين"""
        message = f"""
🔒 **اشتراك مطلوب للوصول للبوت**

عذراً، هذا البوت متاح للمشتركين فقط.

**للاشتراك تواصل مع:**
👤 **أسامة التبعي**
📱 **Telegram ID:** {ADMIN_ID}
📞 **رقم الهاتف:** {ADMIN_ID}

💰 **خدمات البوت:**
🚀 مراقبة ضخ السيولة 24/7
📊 تنبيهات فورية للعملات الرقمية والفوركس
⚙️ إعدادات قابلة للتخصيص
📈 إحصائيات مفصلة

**اضغط الزر للتواصل مع المدير:**
        """
        
        # أزرار التواصل مع المدير
        keyboard = [
            [InlineKeyboardButton("📱 تواصل مع أسامة التبعي", url=f"tg://user?id={ADMIN_ID}")],
            [InlineKeyboardButton("🔄 فحص حالة الاشتراك", callback_data="check_subscription")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await self.bot.send_message(
            chat_id=chat_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )
    
    async def send_main_menu(self, chat_id, user_id):
        """إرسال القائمة الرئيسية للمستخدمين المشتركين"""
        # فحص الاشتراك أولاً
        if not self.is_user_subscribed(user_id) and not self.is_admin(user_id):
            await self.send_subscription_required_message(chat_id)
            return
        
        # تحديد حالة البوت ونوع المستخدم
        status_emoji = "🟢" if self.monitoring_active else "🔴"
        status_text = "يعمل" if self.monitoring_active else "متوقف"
        user_type = "👑 مدير" if self.is_admin(user_id) else "👤 مشترك"
        
        # بناء لوحة الأزرار الأساسية
        keyboard = [
            [
                InlineKeyboardButton("📊 حالة البوت", callback_data="status"),
                InlineKeyboardButton("📈 إحصائياتي", callback_data="mystats")
            ],
            [
                InlineKeyboardButton("🔔 إدارة الاشتراكات", callback_data="manage_subscriptions"),
                InlineKeyboardButton("⚙️ الإعدادات", callback_data="settings")
            ]
        ]
        
        # إضافة أزرار خاصة بالمدير فقط
        if self.is_admin(user_id):
            keyboard.extend([
                [
                    InlineKeyboardButton("👥 إدارة المستخدمين", callback_data="admin_users"),
                    InlineKeyboardButton("📊 إحصائيات عامة", callback_data="admin_stats")
                ],
                [
                    InlineKeyboardButton(
                        "🛑 إيقاف البوت" if self.monitoring_active else "▶️ تشغيل البوت", 
                        callback_data="toggle_monitoring"
                    )
                ]
            ])
        
        # أزرار المساعدة والتحديث
        keyboard.extend([
            [
                InlineKeyboardButton("❓ المساعدة", callback_data="help"),
                InlineKeyboardButton("🔄 تحديث", callback_data="refresh")
            ]
        ])
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        # نص الرسالة الرئيسية
        message = f"""
🚀 **بوت مراقبة ضخ السيولة**

{status_emoji} **حالة البوت:** {status_text}
{user_type} **المستخدم:** {user_id}
📊 **العملات المراقبة:** {len(self.crypto_pairs)} رقمية + {len(self.forex_pairs)} فوركس
👥 **المشتركين:** {len(users_data["subscribed_users"])} مستخدم

⏰ **الوقت الحالي:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**استخدم الأزرار أدناه للتنقل:**
        """
        
        await self.bot.send_message(
            chat_id=chat_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )
    
    async def show_status(self, query):
        """عرض حالة البوت التفصيلية"""
        status_emoji = "🟢" if self.monitoring_active else "🔴"
        status_text = "يعمل ويراقب الأسواق" if self.monitoring_active else "متوقف مؤقتاً"
        
        message = f"""
📊 **حالة البوت التفصيلية**

{status_emoji} **الحالة:** {status_text}
⏰ **فترة المراقبة:** كل 30 ثانية
📈 **العملات الرقمية:** {len(self.crypto_pairs)} عملة
💱 **أزواج الفوركس:** {len(self.forex_pairs)} زوج
👥 **المستخدمين المشتركين:** {len(users_data["subscribed_users"])}

**العملات الرقمية المراقبة:**
🪙 {', '.join(self.crypto_pairs[:5])}
🪙 {', '.join(self.crypto_pairs[5:])}

**أزواج الفوركس المراقبة:**
💱 {', '.join(self.forex_pairs[:5])}
💱 {', '.join(self.forex_pairs[5:])}

⏰ **آخر تحديث:** {datetime.now().strftime('%H:%M:%S')}
        """
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )
    
    async def show_mystats(self, query, user_id):
        """عرض إحصائيات المستخدم الشخصية"""
        user_id = str(user_id)
        
        # الحصول على إعدادات المستخدم
        user_settings = users_data["user_settings"].get(user_id, {})
        user_subscriptions = users_data["user_subscriptions"].get(user_id, [])
        
        message = f"""
📈 **إحصائياتك الشخصية**

👤 **معرف المستخدم:** {user_id}
📊 **اشتراكاتك في العملات:** {len(user_subscriptions)}
⚙️ **حد الحجم:** {user_settings.get('volume_threshold', 3.0)}x
📈 **حد السعر:** {user_settings.get('price_threshold', 5.0)}%
🔔 **التنبيهات:** {'مفعلة' if user_settings.get('notifications_enabled', True) else 'معطلة'}

**العملات المشترك بها:**
        """
        
        if user_subscriptions:
            for i, symbol in enumerate(user_subscriptions, 1):
                message += f"{i}. {symbol}\n"
        else:
            message += "📭 لا توجد اشتراكات في عملات محددة\n"
        
        message += f"\n📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d')}"
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

    async def show_help(self, query):
        """عرض دليل المساعدة"""
        message = f"""
❓ **دليل استخدام البوت**

**🎛️ الأوامر الأساسية:**
/start - بدء البوت وعرض القائمة الرئيسية
/menu - عرض القائمة الرئيسية

**🔔 إدارة الاشتراكات في العملات:**
/subscribe_crypto [SYMBOL] - الاشتراك في عملة رقمية
/subscribe_forex [SYMBOL] - الاشتراك في زوج فوركس
/unsubscribe [SYMBOL] - إلغاء الاشتراك

**⚙️ تعديل الإعدادات:**
/set_volume [VALUE] - تعديل حد الحجم
/set_price [VALUE] - تعديل حد السعر
/toggle_notifications - تشغيل/إيقاف التنبيهات

**📝 أمثلة عملية:**
`/subscribe_crypto BTC/USDT`
`/subscribe_forex EUR/USD`
`/set_volume 4.0`
`/set_price 7.0`

**👑 أوامر المدير:**
/add_user [USER_ID] - إضافة مستخدم
/remove_user [USER_ID] - إزالة مستخدم
/list_users - عرض المستخدمين

**🚨 معايير التنبيه:**
• زيادة الحجم حسب الحد المحدد
• تغيير السعر حسب النسبة المحددة

**💡 نصائح:**
• استخدم الأزرار للتنقل السريع
• أرسل الأوامر كرسائل نصية منفصلة
• البوت يعمل 24/7 تلقائياً

**📞 للدعم الفني:**
تواصل مع أسامة التبعي: {ADMIN_ID}
        """

        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

    async def toggle_monitoring(self, query):
        """تشغيل/إيقاف مراقبة البوت (للمدير فقط)"""
        self.monitoring_active = not self.monitoring_active

        if self.monitoring_active:
            message = "▶️ **تم تشغيل مراقبة البوت**\n\nالبوت يعمل الآن ويراقب الأسواق. سيتم إرسال التنبيهات عند ضخ السيولة."
        else:
            message = "🛑 **تم إيقاف مراقبة البوت**\n\nالبوت متوقف مؤقتاً. لن يتم إرسال تنبيهات حتى إعادة التشغيل."

        keyboard = [
            [InlineKeyboardButton(
                "🛑 إيقاف البوت" if self.monitoring_active else "▶️ تشغيل البوت",
                callback_data="toggle_monitoring"
            )],
            [InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

    async def process_updates(self):
        """معالجة التحديثات الواردة من تيليجرام"""
        try:
            # الحصول على التحديثات الجديدة
            updates = await self.bot.get_updates(offset=self.last_update_id + 1, timeout=1)

            for update in updates:
                self.last_update_id = update.update_id

                # معالجة الرسائل النصية
                if update.message:
                    await self.handle_message(update.message)
                # معالجة ضغطات الأزرار
                elif update.callback_query:
                    await self.handle_callback(update.callback_query)

        except Exception as e:
            if "timed out" not in str(e).lower():
                print(f"❌ خطأ في معالجة التحديثات: {e}")

    async def handle_message(self, message):
        """معالجة الرسائل النصية"""
        try:
            text = message.text
            chat_id = message.chat_id
            user_id = str(message.from_user.id)

            # أوامر البدء والقائمة الرئيسية
            if text.startswith('/start') or text.startswith('/menu'):
                await self.send_main_menu(chat_id, user_id)

            # أوامر المدير لإدارة المستخدمين
            elif text.startswith('/add_user') and self.is_admin(user_id):
                await self.handle_add_user(message)

            elif text.startswith('/remove_user') and self.is_admin(user_id):
                await self.handle_remove_user(message)

            elif text.startswith('/list_users') and self.is_admin(user_id):
                await self.handle_list_users(message)

            # أوامر الاشتراك في العملات (للمشتركين والمدير)
            elif text.startswith('/subscribe_crypto') and (self.is_user_subscribed(user_id) or self.is_admin(user_id)):
                await self.handle_subscribe_crypto(message)

            elif text.startswith('/subscribe_forex') and (self.is_user_subscribed(user_id) or self.is_admin(user_id)):
                await self.handle_subscribe_forex(message)

            elif text.startswith('/unsubscribe') and (self.is_user_subscribed(user_id) or self.is_admin(user_id)):
                await self.handle_unsubscribe_symbol(message)

            # أوامر تعديل الإعدادات
            elif text.startswith('/set_volume') and (self.is_user_subscribed(user_id) or self.is_admin(user_id)):
                await self.handle_set_volume(message)

            elif text.startswith('/set_price') and (self.is_user_subscribed(user_id) or self.is_admin(user_id)):
                await self.handle_set_price(message)

            elif text.startswith('/toggle_notifications') and (self.is_user_subscribed(user_id) or self.is_admin(user_id)):
                await self.handle_toggle_notifications(message)

            # رسالة افتراضية
            else:
                if not self.is_user_subscribed(user_id) and not self.is_admin(user_id):
                    await self.send_subscription_required_message(chat_id)
                else:
                    keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="menu")]]
                    reply_markup = InlineKeyboardMarkup(keyboard)

                    await self.bot.send_message(
                        chat_id=chat_id,
                        text="🤖 استخدم الأزرار أو الأوامر للتحكم في البوت\n\nاكتب /help للمساعدة",
                        parse_mode='Markdown',
                        reply_markup=reply_markup
                    )

        except Exception as e:
            print(f"❌ خطأ في معالجة الرسالة: {e}")

    async def handle_callback(self, callback_query):
        """معالجة ضغطات الأزرار"""
        try:
            data = callback_query.data
            chat_id = callback_query.message.chat_id
            user_id = str(callback_query.from_user.id)

            # الرد على الضغطة
            await self.bot.answer_callback_query(callback_query.id)

            # فحص الاشتراك للمستخدمين غير المديرين
            if not self.is_user_subscribed(user_id) and not self.is_admin(user_id):
                if data == "check_subscription":
                    await self.check_subscription_status(callback_query)
                else:
                    await self.send_subscription_required_message(chat_id)
                return

            # معالجة الأزرار المختلفة
            if data == "status":
                await self.show_status(callback_query)
            elif data == "mystats":
                await self.show_mystats(callback_query, user_id)
            elif data == "manage_subscriptions":
                await self.show_manage_subscriptions(callback_query)
            elif data == "settings":
                await self.show_settings(callback_query, user_id)
            elif data == "help":
                await self.show_help(callback_query)
            elif data == "toggle_monitoring" and self.is_admin(user_id):
                await self.toggle_monitoring(callback_query)
            elif data == "admin_users" and self.is_admin(user_id):
                await self.show_admin_users(callback_query)
            elif data == "admin_stats" and self.is_admin(user_id):
                await self.show_admin_stats(callback_query)
            elif data == "refresh":
                await self.refresh_menu(callback_query, user_id)
            elif data == "menu" or data == "back":
                await self.back_to_menu(callback_query, user_id)

        except Exception as e:
            print(f"❌ خطأ في معالجة الزر: {e}")

    # ===== وظائف معالجة الأوامر =====
    # Command Handler Functions

    async def handle_add_user(self, message):
        """إضافة مستخدم جديد (للمدير فقط)"""
        parts = message.text.split()
        if len(parts) < 2:
            await self.bot.send_message(
                message.chat_id,
                "❌ **صيغة خاطئة**\n\nالصيغة الصحيحة: `/add_user USER_ID`",
                parse_mode='Markdown'
            )
            return

        new_user_id = parts[1]
        success = self.add_user_subscription(new_user_id)

        if success:
            response = f"✅ **تم إضافة المستخدم بنجاح**\n\nمعرف المستخدم: {new_user_id}\nيمكنه الآن استخدام البوت"
        else:
            response = f"❌ **المستخدم موجود بالفعل**\n\nمعرف المستخدم: {new_user_id}"

        keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="menu")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await self.bot.send_message(
            message.chat_id,
            response,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

    async def handle_remove_user(self, message):
        """إزالة مستخدم (للمدير فقط)"""
        parts = message.text.split()
        if len(parts) < 2:
            await self.bot.send_message(
                message.chat_id,
                "❌ **صيغة خاطئة**\n\nالصيغة الصحيحة: `/remove_user USER_ID`",
                parse_mode='Markdown'
            )
            return

        user_to_remove = parts[1]
        success = self.remove_user_subscription(user_to_remove)

        if success:
            response = f"✅ **تم إزالة المستخدم بنجاح**\n\nمعرف المستخدم: {user_to_remove}\nلم يعد بإمكانه استخدام البوت"
        else:
            response = f"❌ **المستخدم غير موجود**\n\nمعرف المستخدم: {user_to_remove}"

        keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="menu")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await self.bot.send_message(
            message.chat_id,
            response,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

    async def handle_list_users(self, message):
        """عرض قائمة المستخدمين (للمدير فقط)"""
        users_list = users_data["subscribed_users"]

        if not users_list:
            response = "📭 **لا يوجد مستخدمين مشتركين حالياً**"
        else:
            response = f"👥 **قائمة المستخدمين المشتركين ({len(users_list)} مستخدم):**\n\n"
            for i, user_id in enumerate(users_list, 1):
                response += f"{i}. {user_id}\n"

        keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="menu")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await self.bot.send_message(
            message.chat_id,
            response,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

    async def handle_subscribe_crypto(self, message):
        """الاشتراك في عملة رقمية"""
        parts = message.text.split()
        user_id = str(message.from_user.id)

        if len(parts) < 2:
            await self.bot.send_message(
                message.chat_id,
                "❌ **صيغة خاطئة**\n\nالصيغة الصحيحة: `/subscribe_crypto BTC/USDT`",
                parse_mode='Markdown'
            )
            return

        symbol = parts[1].upper()

        # التحقق من أن العملة موجودة في القائمة
        if symbol not in self.crypto_pairs:
            await self.bot.send_message(
                message.chat_id,
                f"❌ **العملة غير مدعومة**\n\nالعملة {symbol} غير موجودة في قائمة العملات المراقبة",
                parse_mode='Markdown'
            )
            return

        # إضافة الاشتراك
        if user_id not in users_data["user_subscriptions"]:
            users_data["user_subscriptions"][user_id] = []

        if symbol not in users_data["user_subscriptions"][user_id]:
            users_data["user_subscriptions"][user_id].append(symbol)
            save_users_data(users_data)
            response = f"✅ **تم الاشتراك بنجاح**\n\n🪙 العملة: {symbol}\nستصلك تنبيهات عند ضخ السيولة"
        else:
            response = f"❌ **أنت مشترك بالفعل**\n\n🪙 العملة: {symbol}"

        keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="menu")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await self.bot.send_message(
            message.chat_id,
            response,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

    async def handle_subscribe_forex(self, message):
        """الاشتراك في زوج فوركس"""
        parts = message.text.split()
        user_id = str(message.from_user.id)

        if len(parts) < 2:
            await self.bot.send_message(
                message.chat_id,
                "❌ **صيغة خاطئة**\n\nالصيغة الصحيحة: `/subscribe_forex EUR/USD`",
                parse_mode='Markdown'
            )
            return

        symbol = parts[1].upper()

        # التحقق من أن الزوج موجود في القائمة
        if symbol not in self.forex_pairs:
            await self.bot.send_message(
                message.chat_id,
                f"❌ **زوج الفوركس غير مدعوم**\n\nالزوج {symbol} غير موجود في قائمة الأزواج المراقبة",
                parse_mode='Markdown'
            )
            return

        # إضافة الاشتراك
        if user_id not in users_data["user_subscriptions"]:
            users_data["user_subscriptions"][user_id] = []

        if symbol not in users_data["user_subscriptions"][user_id]:
            users_data["user_subscriptions"][user_id].append(symbol)
            save_users_data(users_data)
            response = f"✅ **تم الاشتراك بنجاح**\n\n💱 زوج الفوركس: {symbol}\nستصلك تنبيهات عند ضخ السيولة"
        else:
            response = f"❌ **أنت مشترك بالفعل**\n\n💱 زوج الفوركس: {symbol}"

        keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="menu")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await self.bot.send_message(
            message.chat_id,
            response,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

    # ===== وظائف مفقودة =====
    # Missing Functions

    async def show_manage_subscriptions(self, query):
        """عرض قائمة إدارة الاشتراكات في العملات"""
        message = """
🔔 **إدارة اشتراكاتك في العملات**

**للاشتراك في عملة رقمية:**
`/subscribe_crypto BTC/USDT`
`/subscribe_crypto ETH/USDT`

**للاشتراك في زوج فوركس:**
`/subscribe_forex EUR/USD`
`/subscribe_forex GBP/USD`

**لإلغاء الاشتراك:**
`/unsubscribe BTC/USDT`

**العملات المتاحة:**

🪙 **العملات الرقمية:**
BTC/USDT, ETH/USDT, BNB/USDT, ADA/USDT, SOL/USDT
XRP/USDT, DOT/USDT, DOGE/USDT, AVAX/USDT, MATIC/USDT

💱 **أزواج الفوركس:**
EUR/USD, GBP/USD, USD/JPY, USD/CHF, AUD/USD
USD/CAD, NZD/USD, EUR/GBP, EUR/JPY, GBP/JPY

**ملاحظة:** أرسل الأوامر كرسائل نصية منفصلة
        """

        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

    async def show_settings(self, query, user_id):
        """عرض إعدادات المستخدم"""
        user_id = str(user_id)
        user_settings = users_data["user_settings"].get(user_id, {})

        message = f"""
⚙️ **إعدادات البوت الخاصة بك**

**الحدود الحالية:**
📊 **حد الحجم:** {user_settings.get('volume_threshold', 3.0)}x
📈 **حد السعر:** {user_settings.get('price_threshold', 5.0)}%
🔔 **التنبيهات:** {'مفعلة' if user_settings.get('notifications_enabled', True) else 'معطلة'}

**لتعديل الإعدادات أرسل:**
`/set_volume 4.0` - لتعديل حد الحجم إلى 4 أضعاف
`/set_price 7.0` - لتعديل حد السعر إلى 7%
`/toggle_notifications` - لتشغيل/إيقاف التنبيهات

**شرح الإعدادات:**
• **حد الحجم:** الحد الأدنى لزيادة الحجم لإرسال تنبيه
• **حد السعر:** الحد الأدنى لتغيير السعر لإرسال تنبيه
• **التنبيهات:** تشغيل أو إيقاف جميع التنبيهات
        """

        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

    async def show_admin_users(self, query):
        """عرض قائمة إدارة المستخدمين (للمدير فقط)"""
        subscribed_count = len(users_data["subscribed_users"])

        message = f"""
👥 **إدارة المستخدمين**

📊 **إجمالي المشتركين:** {subscribed_count} مستخدم

**أوامر إدارة المستخدمين:**
`/add_user USER_ID` - إضافة مستخدم جديد
`/remove_user USER_ID` - إزالة مستخدم
`/list_users` - عرض قائمة جميع المستخدمين

**المستخدمين المشتركين حالياً:**
        """

        if users_data["subscribed_users"]:
            for i, user_id in enumerate(users_data["subscribed_users"][:10], 1):
                message += f"{i}. {user_id}\n"

            if len(users_data["subscribed_users"]) > 10:
                message += f"... و {len(users_data['subscribed_users']) - 10} مستخدم آخر\n"
        else:
            message += "📭 لا يوجد مستخدمين مشتركين حالياً\n"

        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

    async def show_admin_stats(self, query):
        """عرض الإحصائيات العامة (للمدير فقط)"""
        total_users = len(users_data["subscribed_users"])
        total_subscriptions = sum(len(subs) for subs in users_data["user_subscriptions"].values())

        message = f"""
📊 **الإحصائيات العامة للبوت**

👥 **إجمالي المستخدمين:** {total_users}
🔔 **إجمالي الاشتراكات:** {total_subscriptions}
📈 **العملات المراقبة:** {len(self.crypto_pairs + self.forex_pairs)}
{("🟢 **حالة البوت:** يعمل" if self.monitoring_active else "🔴 **حالة البوت:** متوقف")}

**إحصائيات مفصلة:**
🪙 **العملات الرقمية:** {len(self.crypto_pairs)} عملة
💱 **أزواج الفوركس:** {len(self.forex_pairs)} زوج

**أكثر العملات اشتراكاً:**
        """

        # حساب أكثر العملات اشتراكاً
        symbol_count = {}
        for user_subs in users_data["user_subscriptions"].values():
            for symbol in user_subs:
                symbol_count[symbol] = symbol_count.get(symbol, 0) + 1

        if symbol_count:
            sorted_symbols = sorted(symbol_count.items(), key=lambda x: x[1], reverse=True)
            for i, (symbol, count) in enumerate(sorted_symbols[:5], 1):
                message += f"{i}. {symbol}: {count} مشترك\n"
        else:
            message += "📭 لا توجد اشتراكات في عملات محددة\n"

        message += f"\n⏰ **آخر تحديث:** {datetime.now().strftime('%H:%M:%S')}"

        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

    async def check_subscription_status(self, query):
        """فحص حالة اشتراك المستخدم"""
        user_id = str(query.from_user.id)

        if self.is_user_subscribed(user_id) or self.is_admin(user_id):
            await self.back_to_menu(query, user_id)
        else:
            await self.bot.edit_message_text(
                chat_id=query.message.chat_id,
                message_id=query.message.message_id,
                text="❌ **لم يتم تفعيل اشتراكك بعد**\n\nتواصل مع أسامة التبعي لتفعيل الاشتراك.",
                parse_mode='Markdown'
            )
            await asyncio.sleep(2)
            await self.send_subscription_required_message(query.message.chat_id)

    async def refresh_menu(self, query, user_id):
        """تحديث القائمة الرئيسية"""
        await self.bot.edit_message_text(
            chat_id=query.message.chat_id,
            message_id=query.message.message_id,
            text="🔄 **جاري التحديث...**"
        )
        await asyncio.sleep(1)
        await self.back_to_menu(query, user_id)

    async def back_to_menu(self, query, user_id):
        """العودة للقائمة الرئيسية"""
        # حذف الرسالة الحالية وإرسال قائمة جديدة
        try:
            await self.bot.delete_message(
                chat_id=query.message.chat_id,
                message_id=query.message.message_id
            )
        except:
            pass

        await self.send_main_menu(query.message.chat_id, user_id)

    async def send_startup_message(self):
        """إرسال رسالة بدء تشغيل البوت"""
        try:
            message = f"""
🚀 **بوت مراقبة ضخ السيولة**

✅ البوت يعمل بكامل طاقته!
🎛️ أزرار تفاعلية للتحكم الكامل
👥 نظام إدارة اشتراكات متقدم
📊 مراقبة {len(self.crypto_pairs)} عملة رقمية + {len(self.forex_pairs)} زوج فوركس

**للاشتراك في البوت تواصل مع:**
👤 **أسامة التبعي**
📱 **Telegram ID:** {ADMIN_ID}
📞 **رقم الهاتف:** {ADMIN_ID}

**اكتب /start للبدء**
            """

            if CHAT_ID:
                await self.bot.send_message(
                    chat_id=CHAT_ID,
                    text=message,
                    parse_mode='Markdown'
                )
                print("✅ تم إرسال رسالة بدء التشغيل")
        except Exception as e:
            print(f"❌ فشل في إرسال رسالة بدء التشغيل: {e}")

# ===== الدالة الرئيسية لتشغيل البوت =====
# Main Function to Run the Bot

async def run_bot():
    """تشغيل البوت الرئيسي"""
    print("🚀 بدء تشغيل بوت مراقبة ضخ السيولة...")

    # إنشاء مثيل البوت
    bot = LiquidityPumpBot()

    # إرسال رسالة بدء التشغيل
    await bot.send_startup_message()

    print("✅ البوت يعمل الآن...")
    print("🎛️ جميع الأزرار التفاعلية تعمل!")
    print("👥 نظام إدارة الاشتراكات جاهز!")
    print(f"📞 للاشتراك تواصل مع أسامة التبعي: {ADMIN_ID}")

    # حلقة تشغيل البوت
    while True:
        try:
            # معالجة التحديثات من تيليجرام
            await bot.process_updates()

            # هنا يمكن إضافة منطق مراقبة الأسواق
            if bot.monitoring_active:
                # محاكاة مراقبة الأسواق
                # في التطبيق الحقيقي، ستكون هنا دوال مراقبة الأسعار والحجم
                pass

            # انتظار قصير قبل التحديث التالي
            await asyncio.sleep(1)

        except KeyboardInterrupt:
            print("🛑 تم إيقاف البوت بواسطة المستخدم")
            break
        except Exception as e:
            print(f"❌ خطأ في تشغيل البوت: {e}")
            await asyncio.sleep(5)

if __name__ == "__main__":
    """نقطة البداية الرئيسية للبرنامج"""
    print("=" * 50)
    print("🚀 بوت مراقبة ضخ السيولة")
    print("📱 مع أزرار تفاعلية وإدارة اشتراكات")
    print(f"👤 المدير: أسامة التبعي ({ADMIN_ID})")
    print("=" * 50)

    # تشغيل البوت
    asyncio.run(run_bot())
