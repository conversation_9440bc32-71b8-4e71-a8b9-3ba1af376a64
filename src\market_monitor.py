"""
Market monitoring module for cryptocurrency and forex data
"""
import asyncio
import aiohttp
import ccxt.async_support as ccxt
import yfinance as yf
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from loguru import logger

from config import Config

class MarketMonitor:
    """Monitor cryptocurrency and forex markets"""
    
    def __init__(self):
        self.binance_exchange = None
        self.session = None
        self._initialize_exchanges()
    
    def _initialize_exchanges(self):
        """Initialize cryptocurrency exchanges"""
        try:
            # Initialize Binance
            self.binance_exchange = ccxt.binance({
                'apiKey': Config.BINANCE_API_KEY,
                'secret': Config.BINANCE_SECRET_KEY,
                'sandbox': False,  # Set to True for testing
                'enableRateLimit': True,
            })
            
            logger.info("✅ Cryptocurrency exchanges initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize exchanges: {e}")
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
        if self.binance_exchange:
            await self.binance_exchange.close()
    
    async def get_crypto_data(self) -> List[Dict[str, Any]]:
        """Get cryptocurrency market data"""
        crypto_data = []
        
        try:
            # Get data from Binance
            binance_data = await self._get_binance_data()
            crypto_data.extend(binance_data)
            
            # Fallback to CoinGecko if Binance fails
            if not crypto_data:
                coingecko_data = await self._get_coingecko_data()
                crypto_data.extend(coingecko_data)
            
            logger.info(f"📊 Retrieved data for {len(crypto_data)} crypto pairs")
            
        except Exception as e:
            logger.error(f"❌ Error getting crypto data: {e}")
        
        return crypto_data
    
    async def _get_binance_data(self) -> List[Dict[str, Any]]:
        """Get data from Binance API"""
        data = []
        
        try:
            if not self.binance_exchange:
                return data
            
            # Get 24hr ticker statistics
            tickers = await self.binance_exchange.fetch_tickers()
            
            for symbol in Config.CRYPTO_PAIRS:
                if symbol in tickers:
                    ticker = tickers[symbol]
                    
                    # Get additional data
                    ohlcv = await self.binance_exchange.fetch_ohlcv(
                        symbol, '1m', limit=60  # Last 60 minutes
                    )
                    
                    data.append({
                        'symbol': symbol,
                        'exchange': 'binance',
                        'current_price': ticker['last'],
                        'price_change_24h': ticker['change'],
                        'price_change_percent_24h': ticker['percentage'],
                        'volume_24h': ticker['quoteVolume'],
                        'high_24h': ticker['high'],
                        'low_24h': ticker['low'],
                        'timestamp': datetime.now(),
                        'ohlcv_data': ohlcv,
                        'market_type': 'crypto'
                    })
            
            logger.info(f"✅ Retrieved {len(data)} pairs from Binance")
            
        except Exception as e:
            logger.error(f"❌ Error getting Binance data: {e}")
        
        return data
    
    async def _get_coingecko_data(self) -> List[Dict[str, Any]]:
        """Get data from CoinGecko API as fallback"""
        data = []
        
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            # Map symbols to CoinGecko IDs
            symbol_map = {
                'BTC/USDT': 'bitcoin',
                'ETH/USDT': 'ethereum',
                'BNB/USDT': 'binancecoin',
                'ADA/USDT': 'cardano',
                'SOL/USDT': 'solana',
                'XRP/USDT': 'ripple',
                'DOT/USDT': 'polkadot',
                'DOGE/USDT': 'dogecoin',
                'AVAX/USDT': 'avalanche-2',
                'MATIC/USDT': 'matic-network'
            }
            
            coin_ids = ','.join(symbol_map.values())
            url = f"https://api.coingecko.com/api/v3/coins/markets"
            
            params = {
                'vs_currency': 'usd',
                'ids': coin_ids,
                'order': 'market_cap_desc',
                'per_page': 100,
                'page': 1,
                'sparkline': False,
                'price_change_percentage': '1h,24h'
            }
            
            headers = {}
            if Config.COINGECKO_API_KEY:
                headers['X-CG-Pro-API-Key'] = Config.COINGECKO_API_KEY
            
            timeout = aiohttp.ClientTimeout(total=30)
            async with self.session.get(url, params=params, headers=headers, timeout=timeout) as response:
                if response.status == 200:
                    coins_data = await response.json()
                    
                    for coin in coins_data:
                        # Find corresponding symbol
                        symbol = None
                        for sym, coin_id in symbol_map.items():
                            if coin_id == coin['id']:
                                symbol = sym
                                break
                        
                        if symbol:
                            data.append({
                                'symbol': symbol,
                                'exchange': 'coingecko',
                                'current_price': coin['current_price'],
                                'price_change_24h': coin['price_change_24h'],
                                'price_change_percent_24h': coin['price_change_percentage_24h'],
                                'volume_24h': coin['total_volume'],
                                'high_24h': coin['high_24h'],
                                'low_24h': coin['low_24h'],
                                'market_cap': coin['market_cap'],
                                'timestamp': datetime.now(),
                                'market_type': 'crypto'
                            })
                
                logger.info(f"✅ Retrieved {len(data)} pairs from CoinGecko")
        
        except Exception as e:
            logger.error(f"❌ Error getting CoinGecko data: {e}")
        
        return data
    
    async def get_forex_data(self) -> List[Dict[str, Any]]:
        """Get forex market data"""
        forex_data = []
        
        try:
            # Use Alpha Vantage for forex data
            if Config.ALPHA_VANTAGE_API_KEY:
                alpha_vantage_data = await self._get_alpha_vantage_data()
                forex_data.extend(alpha_vantage_data)
            
            # Fallback to Yahoo Finance
            if not forex_data:
                yahoo_data = await self._get_yahoo_forex_data()
                forex_data.extend(yahoo_data)
            
            logger.info(f"💱 Retrieved data for {len(forex_data)} forex pairs")
            
        except Exception as e:
            logger.error(f"❌ Error getting forex data: {e}")
        
        return forex_data
    
    async def _get_alpha_vantage_data(self) -> List[Dict[str, Any]]:
        """Get forex data from Alpha Vantage"""
        data = []
        
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            for pair in Config.FOREX_PAIRS:
                from_currency, to_currency = pair.split('/')
                
                url = "https://www.alphavantage.co/query"
                params = {
                    'function': 'FX_INTRADAY',
                    'from_symbol': from_currency,
                    'to_symbol': to_currency,
                    'interval': '1min',
                    'apikey': Config.ALPHA_VANTAGE_API_KEY
                }
                
                async with self.session.get(url, params=params) as response:
                    if response.status == 200:
                        forex_data = await response.json()
                        
                        if 'Time Series (1min)' in forex_data:
                            time_series = forex_data['Time Series (1min)']
                            latest_time = max(time_series.keys())
                            latest_data = time_series[latest_time]
                            
                            data.append({
                                'symbol': pair,
                                'exchange': 'alpha_vantage',
                                'current_price': float(latest_data['4. close']),
                                'high': float(latest_data['2. high']),
                                'low': float(latest_data['3. low']),
                                'volume': float(latest_data['5. volume']) if '5. volume' in latest_data else 0,
                                'timestamp': datetime.now(),
                                'market_type': 'forex'
                            })
                
                # Rate limiting
                await asyncio.sleep(0.2)
            
            logger.info(f"✅ Retrieved {len(data)} pairs from Alpha Vantage")
            
        except Exception as e:
            logger.error(f"❌ Error getting Alpha Vantage data: {e}")
        
        return data
    
    async def _get_yahoo_forex_data(self) -> List[Dict[str, Any]]:
        """Get forex data from Yahoo Finance as fallback"""
        data = []
        
        try:
            for pair in Config.FOREX_PAIRS:
                # Convert to Yahoo Finance format
                yahoo_symbol = pair.replace('/', '') + '=X'
                
                # Get data
                ticker = yf.Ticker(yahoo_symbol)
                info = ticker.info
                hist = ticker.history(period='1d', interval='1m')
                
                if not hist.empty:
                    latest = hist.iloc[-1]
                    
                    data.append({
                        'symbol': pair,
                        'exchange': 'yahoo',
                        'current_price': latest['Close'],
                        'high': latest['High'],
                        'low': latest['Low'],
                        'volume': latest['Volume'],
                        'timestamp': datetime.now(),
                        'market_type': 'forex'
                    })
            
            logger.info(f"✅ Retrieved {len(data)} pairs from Yahoo Finance")
            
        except Exception as e:
            logger.error(f"❌ Error getting Yahoo Finance data: {e}")
        
        return data
