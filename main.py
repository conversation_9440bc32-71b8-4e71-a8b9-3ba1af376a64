"""
Main entry point for the Liquidity Pump Detection Bot
"""
import asyncio
import os
import sys
from pathlib import Path
from loguru import logger

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from src.telegram_bot import TelegramBot
from src.market_monitor import MarketMonitor
from src.pump_detector import PumpDetector
from src.user_settings import UserSettingsManager
from performance_optimizer import PerformanceOptimizer

async def setup_directories():
    """Create necessary directories"""
    directories = ['data', 'logs', 'src']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)

async def setup_logging():
    """Setup logging configuration"""
    logger.remove()  # Remove default handler
    
    # Add file handler
    logger.add(
        Config.LOG_FILE,
        level=Config.LOG_LEVEL,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        rotation="1 day",
        retention="7 days"
    )
    
    # Add console handler
    logger.add(
        sys.stdout,
        level=Config.LOG_LEVEL,
        format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | <level>{message}</level>"
    )

async def main():
    """Main function to run the bot"""
    try:
        # Setup
        await setup_directories()
        await setup_logging()
        
        logger.info("🚀 Starting Liquidity Pump Detection Bot")
        
        # Validate configuration
        if not Config.validate_config():
            logger.error("❌ Configuration validation failed")
            return
        
        # Initialize components
        telegram_bot = TelegramBot()
        market_monitor = MarketMonitor()
        pump_detector = PumpDetector()
        user_settings = UserSettingsManager()
        performance_optimizer = PerformanceOptimizer()
        
        # Start the bot
        logger.info("✅ Bot initialized successfully")
        logger.info(f"📊 Monitoring {len(Config.CRYPTO_PAIRS)} crypto pairs and {len(Config.FOREX_PAIRS)} forex pairs")
        
        # Run the monitoring loop
        await run_monitoring_loop(telegram_bot, market_monitor, pump_detector, user_settings, performance_optimizer)
        
    except KeyboardInterrupt:
        logger.info("🛑 Bot stopped by user")
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        raise

async def run_monitoring_loop(telegram_bot, market_monitor, pump_detector, user_settings, performance_optimizer):
    """Main monitoring loop"""
    logger.info("🔄 Starting monitoring loop")

    optimization_counter = 0

    while True:
        try:
            # Get market data
            crypto_data = await market_monitor.get_crypto_data()
            forex_data = await market_monitor.get_forex_data()

            # Detect pumps
            crypto_pumps = pump_detector.detect_crypto_pumps(crypto_data)
            forex_pumps = pump_detector.detect_forex_pumps(forex_data)

            # Send alerts (with user settings consideration)
            for pump in crypto_pumps + forex_pumps:
                # For now, send to default chat (can be extended for multiple users)
                if Config.TELEGRAM_CHAT_ID:
                    user_id = Config.TELEGRAM_CHAT_ID
                    if user_settings.should_send_alert(user_id, pump):
                        await telegram_bot.send_pump_alert(pump)
                        user_settings.record_alert_sent(user_id, pump, "Pump alert sent")
                        pump_detector.record_pump_alert(pump)
                else:
                    # Fallback to sending without user settings
                    await telegram_bot.send_pump_alert(pump)
                    pump_detector.record_pump_alert(pump)

            # Run performance optimization every 10 cycles (approximately every 10 minutes)
            optimization_counter += 1
            if optimization_counter >= 10:
                await performance_optimizer.run_optimization_cycle()
                optimization_counter = 0

            # Wait for next check
            await asyncio.sleep(Config.CHECK_INTERVAL_SECONDS)

        except Exception as e:
            logger.error(f"Error in monitoring loop: {e}")
            await asyncio.sleep(30)  # Wait 30 seconds before retrying

if __name__ == "__main__":
    asyncio.run(main())
