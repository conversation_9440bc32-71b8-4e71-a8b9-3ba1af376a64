"""
Run the working interactive bot with all functions
"""
import asyncio
import sys
from pathlib import Path
from datetime import datetime
from loguru import logger

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from working_interactive_bot import WorkingInteractiveBot

async def run_monitoring_loop(bot):
    """Enhanced monitoring loop with full control"""
    logger.info("🔄 Starting working interactive monitoring loop")
    
    while True:
        try:
            # Only monitor if active
            if bot.monitoring_active:
                # Get market data
                crypto_data = await bot.market_monitor.get_crypto_data()
                forex_data = await bot.market_monitor.get_forex_data()
                
                # Detect pumps
                crypto_pumps = bot.pump_detector.detect_crypto_pumps(crypto_data)
                forex_pumps = bot.pump_detector.detect_forex_pumps(forex_data)
                
                # Send alerts
                for pump in crypto_pumps + forex_pumps:
                    if bot.chat_id:
                        user_id = bot.chat_id
                        if bot.user_settings.should_send_alert(user_id, pump):
                            await bot.send_pump_alert(pump)
                            bot.user_settings.record_alert_sent(user_id, pump, "Interactive alert")
                            bot.pump_detector.record_pump_alert(pump)
                    else:
                        await bot.send_pump_alert(pump)
                        bot.pump_detector.record_pump_alert(pump)
                
                logger.info(f"✅ Monitoring cycle completed - Active: {bot.monitoring_active}")
                await asyncio.sleep(Config.CHECK_INTERVAL_SECONDS)
            else:
                # If monitoring is stopped, just wait
                logger.info("⏸️ Monitoring paused")
                await asyncio.sleep(10)
            
        except Exception as e:
            logger.error(f"❌ Error in monitoring loop: {e}")
            await asyncio.sleep(30)

async def main():
    """Main function to run the working interactive bot"""
    try:
        logger.info("🚀 Starting Working Interactive Bot")
        
        # Initialize bot
        bot = WorkingInteractiveBot()
        
        # Send startup message
        await bot.send_startup_message()
        
        # Start bot application
        await bot.application.initialize()
        await bot.application.start()
        await bot.application.updater.start_polling()
        
        # Start monitoring in background
        monitoring_task = asyncio.create_task(run_monitoring_loop(bot))
        
        logger.info("✅ Working interactive bot started successfully")
        logger.info("🎛️ ALL BUTTONS ARE NOW WORKING!")
        
        # Keep running
        try:
            await monitoring_task
        except KeyboardInterrupt:
            logger.info("🛑 Bot stopped by user")
        finally:
            await bot.application.stop()
            
    except Exception as e:
        logger.error(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
