"""
Configuration module for the Liquidity Pump Detection Bot
"""
import os
from dotenv import load_dotenv
from typing import List, Dict, Any

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for the bot"""
    
    # Telegram Configuration
    TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
    TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')
    
    # API Keys
    BINANCE_API_KEY = os.getenv('BINANCE_API_KEY')
    BINANCE_SECRET_KEY = os.getenv('BINANCE_SECRET_KEY')
    ALPHA_VANTAGE_API_KEY = os.getenv('ALPHA_VANTAGE_API_KEY')
    COINGECKO_API_KEY = os.getenv('COINGECKO_API_KEY')
    
    # Monitoring Settings
    CHECK_INTERVAL_SECONDS = int(os.getenv('CHECK_INTERVAL_SECONDS', 60))
    VOLUME_THRESHOLD_MULTIPLIER = float(os.getenv('VOLUME_THRESHOLD_MULTIPLIER', 2.0))
    PRICE_CHANGE_THRESHOLD = float(os.getenv('PRICE_CHANGE_THRESHOLD', 5.0))
    
    # Database
    DATABASE_PATH = os.getenv('DATABASE_PATH', './data/bot_data.db')
    
    # Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', './logs/bot.log')
    
    # Cryptocurrency pairs to monitor
    CRYPTO_PAIRS = [
        'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT',
        'XRP/USDT', 'DOT/USDT', 'DOGE/USDT', 'AVAX/USDT', 'MATIC/USDT'
    ]
    
    # Forex pairs to monitor
    FOREX_PAIRS = [
        'EUR/USD', 'GBP/USD', 'USD/JPY', 'USD/CHF', 'AUD/USD',
        'USD/CAD', 'NZD/USD', 'EUR/GBP', 'EUR/JPY', 'GBP/JPY'
    ]
    
    # Alert thresholds
    ALERT_THRESHOLDS = {
        'volume_spike': 3.0,  # 3x normal volume
        'price_change': 5.0,  # 5% price change
        'time_window': 300,   # 5 minutes
    }
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate that required configuration is present"""
        required_vars = ['TELEGRAM_BOT_TOKEN']
        
        for var in required_vars:
            if not getattr(cls, var):
                print(f"Error: {var} is required but not set")
                return False
        
        return True
    
    @classmethod
    def get_monitored_symbols(cls) -> Dict[str, List[str]]:
        """Get all symbols to monitor"""
        return {
            'crypto': cls.CRYPTO_PAIRS,
            'forex': cls.FOREX_PAIRS
        }
