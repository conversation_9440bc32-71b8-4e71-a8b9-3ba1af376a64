"""
Setup script for the Liquidity Pump Detection Bot
"""
import os
import sys
import subprocess
from pathlib import Path

def create_directories():
    """Create necessary directories"""
    directories = ['data', 'logs', 'src']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

def install_requirements():
    """Install Python requirements"""
    try:
        print("📦 Installing Python requirements...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False
    return True

def setup_env_file():
    """Setup environment file"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        try:
            env_file.write_text(env_example.read_text())
            print("✅ Created .env file from .env.example")
            print("⚠️  Please edit .env file with your actual API keys and tokens")
        except Exception as e:
            print(f"❌ Failed to create .env file: {e}")
    elif env_file.exists():
        print("ℹ️  .env file already exists")
    else:
        print("⚠️  No .env.example file found")

def validate_python_version():
    """Validate Python version"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def main():
    """Main setup function"""
    print("🚀 Setting up Liquidity Pump Detection Bot...")
    print("=" * 50)
    
    # Validate Python version
    if not validate_python_version():
        return
    
    # Create directories
    create_directories()
    
    # Install requirements
    if not install_requirements():
        return
    
    # Setup environment file
    setup_env_file()
    
    print("\n" + "=" * 50)
    print("✅ Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file with your API keys and tokens")
    print("2. Create a Telegram bot with @BotFather")
    print("3. Get your chat ID")
    print("4. Run: python main.py")
    print("\n📖 For detailed instructions, see README.md")

if __name__ == "__main__":
    main()
