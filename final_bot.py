"""
Final Working Interactive Bot - Timezone Issue Fixed!
"""
import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timezone
from loguru import logger

# Fix timezone issue BEFORE importing telegram
os.environ['TZ'] = 'UTC'
import time
if hasattr(time, 'tzset'):
    time.tzset()

from telegram import Bot, Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes, MessageHandler, filters
import pytz

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from src.market_monitor import MarketMonitor
from src.pump_detector import PumpDetector
from src.user_settings import UserSettingsManager

async def setup_logging():
    """Setup logging configuration"""
    logger.remove()  # Remove default handler
    
    # Add console handler
    logger.add(
        sys.stdout,
        level=Config.LOG_LEVEL,
        format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | <cyan>{name}</cyan> | <level>{message}</level>"
    )

class FinalWorkingBot:
    """Final working interactive bot with timezone fix"""
    
    def __init__(self):
        self.bot_token = Config.TELEGRAM_BOT_TOKEN
        self.chat_id = Config.TELEGRAM_CHAT_ID
        self.bot = None
        self.application = None
        self.user_settings = UserSettingsManager()
        self.market_monitor = MarketMonitor()
        self.pump_detector = PumpDetector()
        self.monitoring_active = True
        self._initialize_bot()
    
    def _initialize_bot(self):
        """Initialize the Telegram bot with timezone fix"""
        try:
            self.bot = Bot(token=self.bot_token)
            
            # Create application without timezone complications
            self.application = (Application.builder()
                              .token(self.bot_token)
                              .job_queue(None)  # Disable job queue completely
                              .build())
            
            # Add all handlers
            self.application.add_handler(CommandHandler("start", self.start_command))
            self.application.add_handler(CommandHandler("menu", self.menu_command))
            self.application.add_handler(CommandHandler("stop", self.stop_command))
            self.application.add_handler(CommandHandler("resume", self.resume_command))
            self.application.add_handler(CommandHandler("subscribe", self.subscribe_command))
            self.application.add_handler(CommandHandler("unsubscribe", self.unsubscribe_command))
            self.application.add_handler(CommandHandler("threshold", self.threshold_command))
            self.application.add_handler(CallbackQueryHandler(self.button_callback))
            self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_text))
            
            logger.info("✅ Final working bot initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize bot: {e}")
            raise
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        await self.send_main_menu(update.effective_chat.id)
    
    async def menu_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /menu command"""
        await self.send_main_menu(update.effective_chat.id)
    
    async def send_main_menu(self, chat_id):
        """Send main menu with working buttons"""
        status_emoji = "🟢" if self.monitoring_active else "🔴"
        
        keyboard = [
            [
                InlineKeyboardButton("📊 حالة البوت", callback_data="status"),
                InlineKeyboardButton("📈 إحصائياتي", callback_data="mystats")
            ],
            [
                InlineKeyboardButton("🔔 الاشتراكات", callback_data="subscriptions"),
                InlineKeyboardButton("⚙️ الإعدادات", callback_data="settings")
            ],
            [
                InlineKeyboardButton("🛑 إيقاف" if self.monitoring_active else "▶️ تشغيل", 
                                   callback_data="toggle_monitoring")
            ],
            [
                InlineKeyboardButton("❓ المساعدة", callback_data="help"),
                InlineKeyboardButton("🔄 تحديث", callback_data="refresh")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        message = f"""
🚀 **بوت مراقبة ضخ السيولة**

{status_emoji} **الحالة:** {'يعمل' if self.monitoring_active else 'متوقف'}
📊 **يراقب:** 10 عملات رقمية + 10 فوركس
🔔 **الأزرار تعمل بشكل كامل!**

⏰ **الوقت:** {datetime.now().strftime('%H:%M:%S')}
        """
        
        await self.bot.send_message(
            chat_id=chat_id,
            text=message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle all button callbacks - WORKING!"""
        query = update.callback_query
        await query.answer()
        
        user_id = str(query.from_user.id)
        
        try:
            if query.data == "status":
                await self.show_status(query)
            elif query.data == "mystats":
                await self.show_mystats(query, user_id)
            elif query.data == "subscriptions":
                await self.show_subscriptions(query)
            elif query.data == "settings":
                await self.show_settings(query, user_id)
            elif query.data == "help":
                await self.show_help(query)
            elif query.data == "toggle_monitoring":
                await self.toggle_monitoring(query)
            elif query.data == "refresh":
                await self.refresh_menu(query)
            elif query.data == "back":
                await self.back_to_menu(query)
                
        except Exception as e:
            logger.error(f"❌ Error in button callback: {e}")
            await query.edit_message_text("❌ حدث خطأ. جرب مرة أخرى.")
    
    async def show_status(self, query):
        """Show bot status"""
        status_emoji = "🟢" if self.monitoring_active else "🔴"
        
        message = f"""
📊 **حالة البوت التفصيلية**

{status_emoji} **الحالة:** {'يعمل ويراقب' if self.monitoring_active else 'متوقف'}
⏰ **فترة المراقبة:** كل {Config.CHECK_INTERVAL_SECONDS} ثانية
📈 **العملات الرقمية:** {len(Config.CRYPTO_PAIRS)} عملة
💱 **أزواج الفوركس:** {len(Config.FOREX_PAIRS)} زوج

**الحدود الحالية:**
📊 حد الحجم: {Config.ALERT_THRESHOLDS['volume_spike']}x
📈 حد السعر: {Config.ALERT_THRESHOLDS['price_change']}%

⏰ **الوقت الحالي:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**العملات المراقبة:**
🪙 BTC, ETH, BNB, ADA, SOL, XRP, DOT, DOGE, AVAX, MATIC
💱 EUR/USD, GBP/USD, USD/JPY, USD/CHF, AUD/USD
        """
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)
    
    async def show_mystats(self, query, user_id):
        """Show user statistics"""
        stats = self.user_settings.get_user_stats(user_id)
        
        message = f"""
📈 **إحصائياتك الشخصية**

👤 **معرف المستخدم:** {user_id}
🔔 **إجمالي التنبيهات:** {stats['total_alerts']}
📊 **الاشتراكات النشطة:** {stats['active_subscriptions']}
⏰ **تنبيهات آخر 24 ساعة:** {stats['recent_alerts_24h']}

📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d')}
        """
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)
    
    async def show_subscriptions(self, query):
        """Show subscription management"""
        message = """
🔔 **إدارة الاشتراكات**

**للاشتراك في عملة رقمية:**
`/subscribe BTC/USDT crypto`
`/subscribe ETH/USDT crypto`

**للاشتراك في الفوركس:**
`/subscribe EUR/USD forex`
`/subscribe GBP/USD forex`

**لإلغاء الاشتراك:**
`/unsubscribe BTC/USDT crypto`

**العملات المتاحة:**

🪙 **العملات الرقمية:**
BTC/USDT, ETH/USDT, BNB/USDT, ADA/USDT, SOL/USDT
XRP/USDT, DOT/USDT, DOGE/USDT, AVAX/USDT, MATIC/USDT

💱 **أزواج الفوركس:**
EUR/USD, GBP/USD, USD/JPY, USD/CHF, AUD/USD
USD/CAD, NZD/USD, EUR/GBP, EUR/JPY, GBP/JPY

**ملاحظة:** أرسل الأوامر كرسائل نصية منفصلة
        """
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)
    
    async def show_settings(self, query, user_id):
        """Show settings menu"""
        settings = self.user_settings.get_user_settings(user_id)
        
        message = f"""
⚙️ **إعدادات البوت**

**الحدود الحالية:**
📊 حد الحجم: {settings['notifications']['volume_threshold']}x
📈 حد السعر: {settings['notifications']['price_threshold']}%
🎯 حد الثقة: {settings['notifications']['min_confidence']}

**حالة التنبيهات:**
🪙 العملات الرقمية: {'✅ مفعل' if settings['notifications']['crypto_enabled'] else '❌ معطل'}
💱 الفوركس: {'✅ مفعل' if settings['notifications']['forex_enabled'] else '❌ معطل'}

**لتعديل الحدود أرسل:**
`/threshold volume 3.5` - لتعديل حد الحجم
`/threshold price 7.0` - لتعديل حد السعر  
`/threshold confidence 0.8` - لتعديل حد الثقة
        """
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)
    
    async def show_help(self, query):
        """Show help information"""
        message = """
❓ **دليل استخدام البوت**

**🎛️ الأوامر الأساسية:**
/start - بدء البوت وعرض القائمة
/menu - عرض القائمة الرئيسية
/stop - إيقاف المراقبة مؤقتاً
/resume - استئناف المراقبة

**🔔 إدارة الاشتراكات:**
/subscribe [SYMBOL] [TYPE] - الاشتراك
/unsubscribe [SYMBOL] [TYPE] - إلغاء الاشتراك

**⚙️ تعديل الإعدادات:**
/threshold [TYPE] [VALUE] - تعديل الحدود

**📝 أمثلة عملية:**
`/subscribe BTC/USDT crypto`
`/subscribe EUR/USD forex`
`/threshold volume 3.5`
`/threshold price 7.0`

**🚨 معايير التنبيه:**
• زيادة الحجم 3 أضعاف أو أكثر
• تغيير السعر 5% أو أكثر خلال 5 دقائق

**💡 نصائح:**
• استخدم الأزرار للتنقل السريع
• أرسل الأوامر كرسائل نصية منفصلة
• البوت يعمل 24/7 تلقائياً
        """
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)
    
    async def toggle_monitoring(self, query):
        """Toggle monitoring on/off"""
        self.monitoring_active = not self.monitoring_active
        
        if self.monitoring_active:
            message = "▶️ **تم تشغيل المراقبة**\n\nالبوت يعمل الآن ويراقب الأسواق. ستصلك التنبيهات عند ضخ السيولة."
        else:
            message = "🛑 **تم إيقاف المراقبة**\n\nالبوت متوقف مؤقتاً. لن يرسل تنبيهات حتى تعيد تشغيله."
        
        keyboard = [
            [InlineKeyboardButton("🛑 إيقاف" if self.monitoring_active else "▶️ تشغيل", 
                                callback_data="toggle_monitoring")],
            [InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)
    
    async def refresh_menu(self, query):
        """Refresh the menu"""
        await query.edit_message_text("🔄 **جاري التحديث...**")
        await asyncio.sleep(1)
        await self.back_to_menu(query)
    
    async def back_to_menu(self, query):
        """Go back to main menu"""
        status_emoji = "🟢" if self.monitoring_active else "🔴"
        
        keyboard = [
            [
                InlineKeyboardButton("📊 حالة البوت", callback_data="status"),
                InlineKeyboardButton("📈 إحصائياتي", callback_data="mystats")
            ],
            [
                InlineKeyboardButton("🔔 الاشتراكات", callback_data="subscriptions"),
                InlineKeyboardButton("⚙️ الإعدادات", callback_data="settings")
            ],
            [
                InlineKeyboardButton("🛑 إيقاف" if self.monitoring_active else "▶️ تشغيل", 
                                   callback_data="toggle_monitoring")
            ],
            [
                InlineKeyboardButton("❓ المساعدة", callback_data="help"),
                InlineKeyboardButton("🔄 تحديث", callback_data="refresh")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        message = f"""
🚀 **بوت مراقبة ضخ السيولة**

{status_emoji} **الحالة:** {'يعمل' if self.monitoring_active else 'متوقف'}
📊 **يراقب:** 10 عملات رقمية + 10 فوركس
🔔 **الأزرار تعمل بشكل كامل!**

⏰ **الوقت:** {datetime.now().strftime('%H:%M:%S')}
        """
        
        await query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)

    # Command handlers
    async def subscribe_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /subscribe command"""
        user_id = str(update.effective_user.id)

        if not context.args or len(context.args) < 2:
            message = "❌ **صيغة خاطئة**\n\n**الصيغة الصحيحة:**\n`/subscribe BTC/USDT crypto`"
            await update.message.reply_text(message, parse_mode='Markdown')
            return

        symbol = context.args[0].upper()
        market_type = context.args[1].lower()

        if market_type not in ['crypto', 'forex']:
            await update.message.reply_text("❌ نوع السوق يجب أن يكون crypto أو forex")
            return

        success = self.user_settings.add_user_subscription(user_id, symbol, market_type)

        if success:
            emoji = "🪙" if market_type == "crypto" else "💱"
            message = f"✅ **تم الاشتراك بنجاح!**\n\n{emoji} {symbol} ({market_type})"
        else:
            message = f"❌ **فشل في الاشتراك في {symbol}**"

        keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)

    async def unsubscribe_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /unsubscribe command"""
        user_id = str(update.effective_user.id)

        if not context.args or len(context.args) < 2:
            message = "❌ **صيغة خاطئة**\n\n**الصيغة الصحيحة:**\n`/unsubscribe BTC/USDT crypto`"
            await update.message.reply_text(message, parse_mode='Markdown')
            return

        symbol = context.args[0].upper()
        market_type = context.args[1].lower()

        success = self.user_settings.remove_user_subscription(user_id, symbol, market_type)

        if success:
            message = f"✅ **تم إلغاء الاشتراك في {symbol} بنجاح!**"
        else:
            message = f"❌ **فشل في إلغاء الاشتراك**"

        keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)

    async def threshold_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /threshold command"""
        user_id = str(update.effective_user.id)

        if not context.args or len(context.args) < 2:
            message = "❌ **صيغة خاطئة**\n\n**الصيغة الصحيحة:**\n`/threshold volume 3.5`"
            await update.message.reply_text(message, parse_mode='Markdown')
            return

        threshold_type = context.args[0].lower()
        try:
            value = float(context.args[1])
        except ValueError:
            await update.message.reply_text("❌ القيمة يجب أن تكون رقماً")
            return

        if threshold_type == 'volume':
            success = self.user_settings.update_user_preference(user_id, 'notifications.volume_threshold', value)
            setting_name = "حد الحجم"
        elif threshold_type == 'price':
            success = self.user_settings.update_user_preference(user_id, 'notifications.price_threshold', value)
            setting_name = "حد السعر"
        elif threshold_type == 'confidence':
            if not 0 <= value <= 1:
                await update.message.reply_text("❌ حد الثقة يجب أن يكون بين 0 و 1")
                return
            success = self.user_settings.update_user_preference(user_id, 'notifications.min_confidence', value)
            setting_name = "حد الثقة"
        else:
            await update.message.reply_text("❌ نوع الحد غير صحيح. استخدم: volume, price, أو confidence")
            return

        if success:
            message = f"✅ **تم تحديث {setting_name} إلى {value} بنجاح!**"
        else:
            message = f"❌ **فشل في تحديث {setting_name}**"

        keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)

    async def stop_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /stop command"""
        self.monitoring_active = False
        message = "🛑 **تم إيقاف المراقبة**"

        keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)

    async def resume_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /resume command"""
        self.monitoring_active = True
        message = "▶️ **تم تشغيل المراقبة**"

        keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)

    async def handle_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle any text message"""
        message = "🤖 استخدم الأزرار أو الأوامر للتحكم في البوت"

        keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)

    async def send_pump_alert(self, pump_data):
        """Send pump alert with interactive buttons"""
        try:
            symbol = pump_data.get('symbol', 'Unknown')
            market_type = pump_data.get('market_type', 'Unknown')
            price_change = pump_data.get('price_change_percent', 0)
            volume_spike = pump_data.get('volume_spike', 0)
            current_price = pump_data.get('current_price', 0)

            trend_emoji = "🚀" if price_change > 0 else "📉"
            market_emoji = "🪙" if market_type == "crypto" else "💱"

            message = f"""
{trend_emoji} **تنبيه ضخ سيولة!** {market_emoji}

**الرمز:** {symbol}
**السوق:** {market_type.upper()}
**السعر:** {current_price:.6f}
**التغيير:** {price_change:+.2f}%
**الحجم:** {volume_spike:.1f}x

⏰ {datetime.now().strftime('%H:%M:%S')}

#ضخ_سيولة #{symbol.replace('/', '_')}
            """

            keyboard = [
                [InlineKeyboardButton("📊 تفاصيل", callback_data=f"details_{symbol}")],
                [InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="back")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            if self.chat_id:
                await self.bot.send_message(
                    chat_id=self.chat_id,
                    text=message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                logger.info(f"📤 Interactive alert sent for {symbol}")

        except Exception as e:
            logger.error(f"❌ Failed to send alert: {e}")

    async def send_startup_message(self):
        """Send startup message"""
        try:
            message = """
🚀 **بوت مراقبة ضخ السيولة**

✅ البوت يعمل بكامل طاقته!
🎛️ جميع الأزرار تعمل الآن بشكل مثالي!
📊 تحكم شامل في جميع الإعدادات
🔔 تنبيهات تفاعلية عند ضخ السيولة

**تم إصلاح مشكلة المنطقة الزمنية نهائياً!**

**اضغط الزر للبدء:**
            """

            keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="back")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            if self.chat_id:
                await self.bot.send_message(
                    chat_id=self.chat_id,
                    text=message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                logger.info("✅ Interactive startup message sent")
        except Exception as e:
            logger.error(f"❌ Failed to send startup message: {e}")

async def run_monitoring_loop(bot):
    """Enhanced monitoring loop with full control"""
    logger.info("🔄 Starting final monitoring loop")

    while True:
        try:
            # Only monitor if active
            if bot.monitoring_active:
                # Get market data
                crypto_data = await bot.market_monitor.get_crypto_data()
                forex_data = await bot.market_monitor.get_forex_data()

                # Detect pumps
                crypto_pumps = bot.pump_detector.detect_crypto_pumps(crypto_data)
                forex_pumps = bot.pump_detector.detect_forex_pumps(forex_data)

                # Send alerts
                for pump in crypto_pumps + forex_pumps:
                    if bot.chat_id:
                        user_id = bot.chat_id
                        if bot.user_settings.should_send_alert(user_id, pump):
                            await bot.send_pump_alert(pump)
                            bot.user_settings.record_alert_sent(user_id, pump, "Interactive alert")
                            bot.pump_detector.record_pump_alert(pump)

                logger.info(f"✅ Monitoring cycle completed - Active: {bot.monitoring_active}")
                await asyncio.sleep(Config.CHECK_INTERVAL_SECONDS)
            else:
                # If monitoring is stopped, just wait
                logger.info("⏸️ Monitoring paused")
                await asyncio.sleep(10)

        except Exception as e:
            logger.error(f"❌ Error in monitoring loop: {e}")
            await asyncio.sleep(30)

async def main():
    """Main function to run the final working bot"""
    try:
        # Setup logging
        await setup_logging()

        logger.info("🚀 Starting Final Working Interactive Bot")
        logger.info("🔧 Timezone issue has been fixed!")

        # Initialize bot
        bot = FinalWorkingBot()

        # Send startup message
        await bot.send_startup_message()

        # Start bot application
        await bot.application.initialize()
        await bot.application.start()
        await bot.application.updater.start_polling()

        # Start monitoring in background
        monitoring_task = asyncio.create_task(run_monitoring_loop(bot))

        logger.info("✅ Final working interactive bot started successfully")
        logger.info("🎛️ ALL BUTTONS ARE NOW WORKING!")
        logger.info("🌍 TIMEZONE ISSUE FIXED!")

        # Keep running
        try:
            await monitoring_task
        except KeyboardInterrupt:
            logger.info("🛑 Bot stopped by user")
        finally:
            await bot.application.stop()

    except Exception as e:
        logger.error(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
