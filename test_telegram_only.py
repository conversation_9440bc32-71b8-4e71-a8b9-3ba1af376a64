"""
Simple test for Telegram bot functionality only
"""
import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config

async def test_telegram_simple():
    """Simple test for Telegram bot"""
    print("🧪 Testing Telegram Bot Connection...")
    
    try:
        from telegram import Bot
        
        # Create bot instance
        bot = Bot(token=Config.TELEGRAM_BOT_TOKEN)
        
        # Test bot info
        bot_info = await bot.get_me()
        print(f"✅ Bot connected: @{bot_info.username}")
        
        # Test sending message
        if Config.TELEGRAM_CHAT_ID:
            await bot.send_message(
                chat_id=Config.TELEGRAM_CHAT_ID,
                text="🧪 اختبار الاتصال - البوت يعمل بشكل صحيح! ✅"
            )
            print("✅ Test message sent successfully!")
        else:
            print("⚠️ No chat ID configured")
        
        return True
        
    except Exception as e:
        print(f"❌ Telegram test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Simple Telegram Test")
    print("=" * 30)
    
    # Validate config
    if not Config.TELEGRAM_BOT_TOKEN:
        print("❌ TELEGRAM_BOT_TOKEN not found in .env file")
        return
    
    success = await test_telegram_simple()
    
    if success:
        print("\n🎉 Telegram bot is working correctly!")
        print("You should have received a test message.")
    else:
        print("\n❌ Telegram bot test failed.")
        print("Please check your bot token and chat ID.")

if __name__ == "__main__":
    asyncio.run(main())
