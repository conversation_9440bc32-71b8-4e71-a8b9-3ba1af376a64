"""
Pump detection algorithm for identifying liquidity pumps in crypto and forex markets
"""
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from loguru import logger
import sqlite3
from pathlib import Path

from config import Config

class PumpDetector:
    """Detect liquidity pumps in market data"""
    
    def __init__(self):
        self.db_path = Config.DATABASE_PATH
        self.historical_data = {}
        self.volume_baselines = {}
        self.price_baselines = {}
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize SQLite database for storing historical data"""
        try:
            # Create data directory if it doesn't exist
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create tables
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    market_type TEXT NOT NULL,
                    price REAL NOT NULL,
                    volume REAL NOT NULL,
                    timestamp DATETIME NOT NULL,
                    exchange TEXT,
                    UNIQUE(symbol, timestamp)
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS pump_alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    market_type TEXT NOT NULL,
                    price_change_percent REAL NOT NULL,
                    volume_spike REAL NOT NULL,
                    timestamp DATETIME NOT NULL,
                    alert_sent BOOLEAN DEFAULT FALSE
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("✅ Database initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize database: {e}")
    
    def detect_crypto_pumps(self, crypto_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect pumps in cryptocurrency data"""
        pumps = []
        
        for data in crypto_data:
            try:
                # Store current data
                self._store_market_data(data)
                
                # Analyze for pump
                pump_result = self._analyze_pump(data)
                
                if pump_result:
                    pumps.append(pump_result)
                    logger.info(f"🚀 Crypto pump detected: {data['symbol']}")
                
            except Exception as e:
                logger.error(f"❌ Error analyzing crypto data for {data.get('symbol', 'Unknown')}: {e}")
        
        return pumps
    
    def detect_forex_pumps(self, forex_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect pumps in forex data"""
        pumps = []
        
        for data in forex_data:
            try:
                # Store current data
                self._store_market_data(data)
                
                # Analyze for pump (forex typically has lower thresholds)
                pump_result = self._analyze_pump(data, is_forex=True)
                
                if pump_result:
                    pumps.append(pump_result)
                    logger.info(f"💱 Forex pump detected: {data['symbol']}")
                
            except Exception as e:
                logger.error(f"❌ Error analyzing forex data for {data.get('symbol', 'Unknown')}: {e}")
        
        return pumps
    
    def _analyze_pump(self, data: Dict[str, Any], is_forex: bool = False) -> Optional[Dict[str, Any]]:
        """Analyze market data for pump patterns"""
        symbol = data['symbol']
        current_price = data.get('current_price', 0)
        current_volume = data.get('volume_24h', 0) or data.get('volume', 0)
        
        # Get historical data
        historical_prices, historical_volumes = self._get_historical_data(symbol)
        
        if len(historical_prices) < 10:  # Need at least 10 data points
            return None
        
        # Calculate baselines
        avg_volume = np.mean(historical_volumes[-20:])  # Last 20 periods
        avg_price = np.mean(historical_prices[-20:])
        
        # Adjust thresholds for forex
        volume_threshold = Config.ALERT_THRESHOLDS['volume_spike']
        price_threshold = Config.ALERT_THRESHOLDS['price_change']
        
        if is_forex:
            volume_threshold *= 0.5  # Lower volume threshold for forex
            price_threshold *= 0.5   # Lower price threshold for forex
        
        # Check for volume spike
        volume_spike = current_volume / avg_volume if avg_volume > 0 else 0
        
        # Check for price change
        price_change_percent = 0
        if 'price_change_percent_24h' in data:
            price_change_percent = abs(data['price_change_percent_24h'])
        elif len(historical_prices) > 0:
            price_change_percent = abs((current_price - historical_prices[-1]) / historical_prices[-1] * 100)
        
        # Additional analysis for crypto with OHLCV data
        if 'ohlcv_data' in data and data['ohlcv_data']:
            short_term_analysis = self._analyze_short_term_movement(data['ohlcv_data'])
            if short_term_analysis:
                price_change_percent = max(price_change_percent, short_term_analysis['max_change'])
                volume_spike = max(volume_spike, short_term_analysis['volume_spike'])
        
        # Check if pump conditions are met
        is_volume_pump = volume_spike >= volume_threshold
        is_price_pump = price_change_percent >= price_threshold
        
        if is_volume_pump or is_price_pump:
            # Additional validation
            if self._validate_pump(symbol, volume_spike, price_change_percent):
                return {
                    'symbol': symbol,
                    'market_type': data['market_type'],
                    'current_price': current_price,
                    'price_change_percent': price_change_percent,
                    'volume_spike': volume_spike,
                    'volume': current_volume,
                    'timestamp': data['timestamp'],
                    'exchange': data.get('exchange', 'unknown'),
                    'confidence': self._calculate_confidence(volume_spike, price_change_percent, is_forex)
                }
        
        return None
    
    def _analyze_short_term_movement(self, ohlcv_data: List[List]) -> Optional[Dict[str, Any]]:
        """Analyze short-term price and volume movements"""
        try:
            if len(ohlcv_data) < 5:
                return None
            
            # Convert to DataFrame for easier analysis
            df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Calculate recent changes (last 5 minutes)
            recent_data = df.tail(5)
            
            # Price analysis
            max_price = recent_data['high'].max()
            min_price = recent_data['low'].min()
            start_price = recent_data['open'].iloc[0]
            end_price = recent_data['close'].iloc[-1]
            
            max_change = max(
                abs((max_price - start_price) / start_price * 100),
                abs((end_price - start_price) / start_price * 100)
            )
            
            # Volume analysis
            avg_volume = df['volume'].mean()
            recent_avg_volume = recent_data['volume'].mean()
            volume_spike = recent_avg_volume / avg_volume if avg_volume > 0 else 0
            
            return {
                'max_change': max_change,
                'volume_spike': volume_spike,
                'price_volatility': (max_price - min_price) / start_price * 100
            }
            
        except Exception as e:
            logger.error(f"❌ Error in short-term analysis: {e}")
            return None
    
    def _validate_pump(self, symbol: str, volume_spike: float, price_change: float) -> bool:
        """Validate pump to reduce false positives"""
        try:
            # Check if we've already sent an alert recently
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            recent_time = datetime.now() - timedelta(minutes=30)
            cursor.execute('''
                SELECT COUNT(*) FROM pump_alerts 
                WHERE symbol = ? AND timestamp > ? AND alert_sent = TRUE
            ''', (symbol, recent_time))
            
            recent_alerts = cursor.fetchone()[0]
            conn.close()
            
            # Don't send alert if we've sent one recently
            if recent_alerts > 0:
                return False
            
            # Additional validation rules
            if volume_spike > 10:  # Very high volume spike might be suspicious
                return price_change > 2  # Require higher price change
            
            if price_change > 20:  # Very high price change
                return volume_spike > 1.5  # Require some volume increase
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error validating pump: {e}")
            return True  # Default to allowing the alert
    
    def _calculate_confidence(self, volume_spike: float, price_change: float, is_forex: bool) -> float:
        """Calculate confidence score for the pump detection"""
        try:
            # Base confidence
            confidence = 0.5
            
            # Volume component
            if volume_spike > 5:
                confidence += 0.3
            elif volume_spike > 3:
                confidence += 0.2
            elif volume_spike > 2:
                confidence += 0.1
            
            # Price component
            if price_change > 10:
                confidence += 0.3
            elif price_change > 5:
                confidence += 0.2
            elif price_change > 3:
                confidence += 0.1
            
            # Adjust for forex
            if is_forex:
                confidence *= 0.8  # Lower confidence for forex
            
            return min(confidence, 1.0)
            
        except Exception:
            return 0.5
    
    def _store_market_data(self, data: Dict[str, Any]):
        """Store market data in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO market_data 
                (symbol, market_type, price, volume, timestamp, exchange)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                data['symbol'],
                data['market_type'],
                data.get('current_price', 0),
                data.get('volume_24h', 0) or data.get('volume', 0),
                data['timestamp'],
                data.get('exchange', 'unknown')
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Error storing market data: {e}")
    
    def _get_historical_data(self, symbol: str) -> Tuple[List[float], List[float]]:
        """Get historical price and volume data for a symbol"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get last 100 data points
            cursor.execute('''
                SELECT price, volume FROM market_data 
                WHERE symbol = ? 
                ORDER BY timestamp DESC 
                LIMIT 100
            ''', (symbol,))
            
            results = cursor.fetchall()
            conn.close()
            
            if results:
                prices = [row[0] for row in reversed(results)]
                volumes = [row[1] for row in reversed(results)]
                return prices, volumes
            
            return [], []
            
        except Exception as e:
            logger.error(f"❌ Error getting historical data: {e}")
            return [], []
    
    def record_pump_alert(self, pump_data: Dict[str, Any]):
        """Record that a pump alert was sent"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO pump_alerts 
                (symbol, market_type, price_change_percent, volume_spike, timestamp, alert_sent)
                VALUES (?, ?, ?, ?, ?, TRUE)
            ''', (
                pump_data['symbol'],
                pump_data['market_type'],
                pump_data['price_change_percent'],
                pump_data['volume_spike'],
                pump_data['timestamp']
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Error recording pump alert: {e}")
