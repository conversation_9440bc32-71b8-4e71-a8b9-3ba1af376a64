# 🚀 دليل التشغيل السريع - بوت مراقبة ضخ السيولة

## ⚡ التشغيل السريع (5 دقائق)

### 1. التحضير الأولي

```bash
# تشغيل الإعداد التلقائي
setup.bat
```

### 2. إنشاء بوت تيليجرام

1. **إنشاء البوت:**
   - افتح تيليجرام وابحث عن `@BotFather`
   - أرسل `/newbot`
   - اختر اسماً للبوت (مثل: `My Pump Alert Bot`)
   - اختر معرفاً للبوت (مثل: `my_pump_alert_bot`)
   - احفظ التوكن الذي ستحصل عليه

2. **الحصول على Chat ID:**
   - أرسل رسالة لبوتك الجديد
   - افتح الرابط: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
   - ابحث عن `"chat":{"id":` واحفظ الرقم

### 3. تكوين البوت

افتح ملف `.env` وأضف:

```env
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here
```

### 4. تشغيل البوت

```bash
# تشغيل البوت
run_bot.bat
```

### 5. اختبار البوت

```bash
# اختبار شامل للبوت
python test_bot.py
```

---

## 🔧 الإعدادات المتقدمة (اختيارية)

### مفاتيح API للحصول على بيانات أكثر

```env
# Binance (للعملات الرقمية)
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key

# Alpha Vantage (للفوركس)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key

# CoinGecko Pro (اختياري)
COINGECKO_API_KEY=your_coingecko_api_key
```

### تخصيص الإعدادات

في ملف `config.py`:

```python
# تخصيص حدود التنبيه
ALERT_THRESHOLDS = {
    'volume_spike': 3.0,  # 3x الحجم العادي
    'price_change': 5.0,  # 5% تغيير السعر
    'time_window': 300,   # 5 دقائق
}

# تخصيص فترة المراقبة
CHECK_INTERVAL_SECONDS = 60  # كل دقيقة
```

---

## 📱 أوامر البوت

### الأوامر الأساسية
- `/start` - بدء البوت
- `/help` - المساعدة
- `/status` - حالة البوت

### إدارة الإعدادات
- `/settings` - عرض الإعدادات
- `/mystats` - إحصائياتك
- `/threshold volume 3.5` - تعديل حد الحجم
- `/threshold price 7.0` - تعديل حد السعر

### إدارة الاشتراكات
- `/subscribe BTC/USDT crypto` - الاشتراك في عملة
- `/subscribe EUR/USD forex` - الاشتراك في زوج فوركس
- `/unsubscribe` - عرض الاشتراكات الحالية
- `/unsubscribe BTC/USDT crypto` - إلغاء الاشتراك

---

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. "TELEGRAM_BOT_TOKEN is required"
**الحل:** تأكد من إضافة التوكن في ملف `.env`

#### 2. "No market data retrieved"
**الحل:** 
- تحقق من الاتصال بالإنترنت
- أضف مفاتيح API للحصول على حدود أعلى

#### 3. "Failed to send test message"
**الحل:**
- تأكد من صحة Chat ID
- تأكد من أن البوت يمكنه إرسال رسائل لك

#### 4. استهلاك عالي للذاكرة
**الحل:**
- قلل من `CHECK_INTERVAL_SECONDS`
- شغل تنظيف قاعدة البيانات: `python -c "from performance_optimizer import PerformanceOptimizer; import asyncio; asyncio.run(PerformanceOptimizer().optimize_database())"`

### فحص السجلات

```bash
# عرض آخر 50 سطر من السجل
tail -50 logs/bot.log

# متابعة السجل مباشرة
tail -f logs/bot.log
```

---

## 📊 مراقبة الأداء

### عرض تقرير الأداء

```python
from performance_optimizer import PerformanceOptimizer
import asyncio

async def show_performance():
    optimizer = PerformanceOptimizer()
    report = await optimizer.get_performance_report()
    print(f"CPU: {report['avg_cpu_percent']}%")
    print(f"Memory: {report['avg_memory_percent']}%")
    print(f"Database: {report['database_size_mb']}MB")

asyncio.run(show_performance())
```

### تحسين الأداء

```python
# تشغيل دورة تحسين كاملة
python -c "from performance_optimizer import PerformanceOptimizer; import asyncio; asyncio.run(PerformanceOptimizer().run_optimization_cycle())"
```

---

## 🎯 نصائح للاستخدام الأمثل

### 1. ضبط الحدود
- ابدأ بحدود عالية (volume: 5x, price: 10%)
- قلل الحدود تدريجياً حسب النتائج

### 2. إدارة التنبيهات
- استخدم الساعات الهادئة لتجنب التنبيهات ليلاً
- حدد عدد التنبيهات القصوى في الساعة

### 3. مراقبة الأداء
- راقب استهلاك الذاكرة والمعالج
- نظف قاعدة البيانات دورياً

### 4. الأمان
- لا تشارك مفاتيح API مع أحد
- استخدم حسابات تجريبية أولاً

---

## 🆘 الحصول على المساعدة

### مشاكل تقنية
1. تحقق من ملف `logs/bot.log`
2. شغل `python test_bot.py` للتشخيص
3. راجع الإعدادات في `.env`

### تحسين الأداء
1. شغل `performance_optimizer.py`
2. راقب استهلاك الموارد
3. قلل من تكرار المراقبة إذا لزم الأمر

### إضافة ميزات جديدة
1. راجع ملف `README.md` للتفاصيل الكاملة
2. استخدم نظام إدارة المستخدمين للتخصيص
3. أضف رموز جديدة في `config.py`

---

**🎉 مبروك! بوتك جاهز للعمل!**

البوت سيراقب الأسواق 24/7 ويرسل تنبيهات فورية عند اكتشاف ضخ السيولة.
