@echo off
echo 🚀 Starting Liquidity Pump Detection Bot...
echo ==========================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist .env (
    echo ⚠️ .env file not found
    echo Please run setup.py first or copy .env.example to .env
    pause
    exit /b 1
)

REM Run the bot
echo ✅ Starting bot...
python main.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo ❌ Bot stopped with an error
    pause
)
