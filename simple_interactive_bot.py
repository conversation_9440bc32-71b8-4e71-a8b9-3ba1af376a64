"""
Simple Interactive Bot without Application framework
"""
import asyncio
import sys
from pathlib import Path
from datetime import datetime
from loguru import logger
from telegram import Bot, InlineKeyboardButton, InlineKeyboardMarkup

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from src.market_monitor import MarketMonitor
from src.pump_detector import PumpDetector
from src.user_settings import UserSettingsManager

class SimpleInteractiveBot:
    """Simple interactive bot with monitoring and buttons"""
    
    def __init__(self):
        self.bot_token = Config.TELEGRAM_BOT_TOKEN
        self.chat_id = Config.TELEGRAM_CHAT_ID
        self.bot = None
        self.user_settings = UserSettingsManager()
        self.market_monitor = MarketMonitor()
        self.pump_detector = PumpDetector()
        self.last_update_id = 0
        self._initialize_bot()
    
    def _initialize_bot(self):
        """Initialize the Telegram bot"""
        try:
            self.bot = Bot(token=self.bot_token)
            logger.info("✅ Simple interactive bot initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize bot: {e}")
            raise
    
    async def send_menu(self, chat_id, message_text="🎛️ **القائمة الرئيسية**"):
        """Send main menu"""
        keyboard = [
            [InlineKeyboardButton("📊 حالة البوت", callback_data="status"),
             InlineKeyboardButton("📈 إحصائياتي", callback_data="mystats")],
            [InlineKeyboardButton("🔔 الاشتراكات", callback_data="subscriptions"),
             InlineKeyboardButton("⚙️ الإعدادات", callback_data="settings")],
            [InlineKeyboardButton("❓ المساعدة", callback_data="help")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await self.bot.send_message(
            chat_id=chat_id,
            text=message_text,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )
    
    async def handle_callback(self, callback_query):
        """Handle button callbacks"""
        try:
            data = callback_query.data
            chat_id = callback_query.message.chat_id
            message_id = callback_query.message.message_id
            
            # Answer the callback query
            await self.bot.answer_callback_query(callback_query.id)
            
            if data == "status":
                message = f"""
📊 **حالة البوت**

🟢 **الحالة:** يعمل ويراقب
⏰ **التحديث:** كل {Config.CHECK_INTERVAL_SECONDS} ثانية
📈 **العملات الرقمية:** {len(Config.CRYPTO_PAIRS)}
💱 **الفوركس:** {len(Config.FOREX_PAIRS)}

**الحدود:**
• الحجم: {Config.ALERT_THRESHOLDS['volume_spike']}x
• السعر: {Config.ALERT_THRESHOLDS['price_change']}%

⏰ **الآن:** {datetime.now().strftime('%H:%M:%S')}
                """
                
            elif data == "mystats":
                user_id = str(callback_query.from_user.id)
                stats = self.user_settings.get_user_stats(user_id)
                
                message = f"""
📈 **إحصائياتك**

🔔 **التنبيهات:** {stats['total_alerts']}
📊 **الاشتراكات:** {stats['active_subscriptions']}
⏰ **آخر 24 ساعة:** {stats['recent_alerts_24h']}

📅 **اليوم:** {datetime.now().strftime('%Y-%m-%d')}
                """
                
            elif data == "subscriptions":
                message = """
🔔 **إدارة الاشتراكات**

**للاشتراك في عملة:**
`/subscribe BTC/USDT crypto`
`/subscribe EUR/USD forex`

**لإلغاء الاشتراك:**
`/unsubscribe BTC/USDT crypto`

**العملات المتاحة:**
🪙 BTC, ETH, BNB, ADA, SOL, XRP
💱 EUR/USD, GBP/USD, USD/JPY

**ملاحظة:** أرسل الأوامر كرسائل نصية
                """
                
            elif data == "settings":
                message = """
⚙️ **الإعدادات**

**لتعديل الحدود أرسل:**
`/threshold volume 3.5`
`/threshold price 7.0`
`/threshold confidence 0.8`

**الحدود الحالية:**
📊 الحجم: 3.0x
📈 السعر: 5.0%
🎯 الثقة: 0.7

**ملاحظة:** أرسل الأوامر كرسائل نصية
                """
                
            elif data == "help":
                message = """
❓ **المساعدة**

**الأوامر المتاحة:**
/start - بدء البوت
/menu - القائمة الرئيسية

**إدارة الاشتراكات:**
/subscribe [SYMBOL] [TYPE]
/unsubscribe [SYMBOL] [TYPE]

**تعديل الإعدادات:**
/threshold [TYPE] [VALUE]

**أمثلة:**
`/subscribe BTC/USDT crypto`
`/subscribe EUR/USD forex`
`/threshold volume 3.5`
`/threshold price 7.0`

**ملاحظة:** البوت يعمل 24/7 تلقائياً
                """
            else:
                message = "🎛️ **القائمة الرئيسية**"
            
            # Add back button
            keyboard = [[InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="menu")]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            # Edit the message
            await self.bot.edit_message_text(
                chat_id=chat_id,
                message_id=message_id,
                text=message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )
            
        except Exception as e:
            logger.error(f"❌ Error handling callback: {e}")
    
    async def handle_command(self, message):
        """Handle text commands"""
        try:
            text = message.text
            chat_id = message.chat_id
            
            if text.startswith('/start') or text.startswith('/menu'):
                welcome_message = """
🚀 **بوت مراقبة ضخ السيولة**

✅ البوت يعمل ويراقب الأسواق!
📊 10 عملات رقمية + 10 أزواج فوركس
🔔 تنبيهات فورية عند ضخ السيولة

**استخدم الأزرار للتحكم:**
                """
                await self.send_menu(chat_id, welcome_message)
                
            elif text.startswith('/subscribe'):
                parts = text.split()
                if len(parts) >= 3:
                    symbol = parts[1].upper()
                    market_type = parts[2].lower()
                    
                    if market_type in ['crypto', 'forex']:
                        user_id = str(message.from_user.id)
                        success = self.user_settings.add_user_subscription(user_id, symbol, market_type)
                        
                        if success:
                            response = f"✅ تم الاشتراك في {symbol} ({market_type})"
                        else:
                            response = f"❌ فشل في الاشتراك في {symbol}"
                    else:
                        response = "❌ نوع السوق يجب أن يكون crypto أو forex"
                else:
                    response = """
❌ **صيغة خاطئة**

**الصيغة الصحيحة:**
`/subscribe BTC/USDT crypto`
`/subscribe EUR/USD forex`
                    """
                
                await self.bot.send_message(chat_id=chat_id, text=response, parse_mode='Markdown')
                
            elif text.startswith('/unsubscribe'):
                parts = text.split()
                if len(parts) >= 3:
                    symbol = parts[1].upper()
                    market_type = parts[2].lower()
                    
                    user_id = str(message.from_user.id)
                    success = self.user_settings.remove_user_subscription(user_id, symbol, market_type)
                    
                    if success:
                        response = f"✅ تم إلغاء الاشتراك في {symbol} ({market_type})"
                    else:
                        response = f"❌ فشل في إلغاء الاشتراك في {symbol}"
                else:
                    response = """
❌ **صيغة خاطئة**

**الصيغة الصحيحة:**
`/unsubscribe BTC/USDT crypto`
`/unsubscribe EUR/USD forex`
                    """
                
                await self.bot.send_message(chat_id=chat_id, text=response, parse_mode='Markdown')
                
            elif text.startswith('/threshold'):
                parts = text.split()
                if len(parts) >= 3:
                    threshold_type = parts[1].lower()
                    try:
                        value = float(parts[2])
                        user_id = str(message.from_user.id)
                        
                        if threshold_type == 'volume':
                            success = self.user_settings.update_user_preference(user_id, 'notifications.volume_threshold', value)
                            setting_name = "حد الحجم"
                        elif threshold_type == 'price':
                            success = self.user_settings.update_user_preference(user_id, 'notifications.price_threshold', value)
                            setting_name = "حد السعر"
                        elif threshold_type == 'confidence':
                            if not 0 <= value <= 1:
                                response = "❌ حد الثقة يجب أن يكون بين 0 و 1"
                                await self.bot.send_message(chat_id=chat_id, text=response)
                                return
                            success = self.user_settings.update_user_preference(user_id, 'notifications.min_confidence', value)
                            setting_name = "حد الثقة"
                        else:
                            response = "❌ نوع الحد غير صحيح. استخدم: volume, price, أو confidence"
                            await self.bot.send_message(chat_id=chat_id, text=response)
                            return
                        
                        if success:
                            response = f"✅ تم تحديث {setting_name} إلى {value}"
                        else:
                            response = f"❌ فشل في تحديث {setting_name}"
                            
                    except ValueError:
                        response = "❌ القيمة يجب أن تكون رقماً"
                else:
                    response = """
❌ **صيغة خاطئة**

**الصيغة الصحيحة:**
`/threshold volume 3.5`
`/threshold price 7.0`
`/threshold confidence 0.8`
                    """
                
                await self.bot.send_message(chat_id=chat_id, text=response, parse_mode='Markdown')
                
        except Exception as e:
            logger.error(f"❌ Error handling command: {e}")
    
    async def process_updates(self):
        """Process incoming updates"""
        try:
            updates = await self.bot.get_updates(offset=self.last_update_id + 1, timeout=1)
            
            for update in updates:
                self.last_update_id = update.update_id
                
                if update.message:
                    await self.handle_command(update.message)
                elif update.callback_query:
                    await self.handle_callback(update.callback_query)
                    
        except Exception as e:
            if "timed out" not in str(e).lower():
                logger.error(f"❌ Error processing updates: {e}")
    
    async def send_pump_alert(self, pump_data):
        """Send pump alert with buttons"""
        try:
            symbol = pump_data.get('symbol', 'Unknown')
            market_type = pump_data.get('market_type', 'Unknown')
            price_change = pump_data.get('price_change_percent', 0)
            volume_spike = pump_data.get('volume_spike', 0)
            current_price = pump_data.get('current_price', 0)
            
            trend_emoji = "🚀" if price_change > 0 else "📉"
            market_emoji = "🪙" if market_type == "crypto" else "💱"
            
            message = f"""
{trend_emoji} **تنبيه ضخ سيولة!** {market_emoji}

**الرمز:** {symbol}
**السوق:** {market_type.upper()}
**السعر:** {current_price:.6f}
**التغيير:** {price_change:+.2f}%
**الحجم:** {volume_spike:.1f}x

⏰ {datetime.now().strftime('%H:%M:%S')}

#ضخ_سيولة #{symbol.replace('/', '_')}
            """
            
            keyboard = [
                [InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            if self.chat_id:
                await self.bot.send_message(
                    chat_id=self.chat_id,
                    text=message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                logger.info(f"📤 Interactive alert sent for {symbol}")
                
        except Exception as e:
            logger.error(f"❌ Failed to send alert: {e}")
    
    async def send_startup_message(self):
        """Send startup message"""
        try:
            message = """
🚀 **بوت مراقبة ضخ السيولة**

✅ البوت يعمل الآن!
📊 يراقب الأسواق كل دقيقة
🔔 سيرسل تنبيهات تفاعلية

**اضغط الزر للوصول للقائمة:**
            """
            
            if self.chat_id:
                await self.send_menu(self.chat_id, message)
                logger.info("✅ Interactive startup message sent")
        except Exception as e:
            logger.error(f"❌ Failed to send startup message: {e}")

async def run_monitoring_loop(bot):
    """Monitoring loop"""
    logger.info("🔄 Starting simple interactive monitoring loop")
    
    while True:
        try:
            # Process Telegram updates
            await bot.process_updates()
            
            # Get market data (every 60 seconds)
            crypto_data = await bot.market_monitor.get_crypto_data()
            forex_data = await bot.market_monitor.get_forex_data()
            
            # Detect pumps
            crypto_pumps = bot.pump_detector.detect_crypto_pumps(crypto_data)
            forex_pumps = bot.pump_detector.detect_forex_pumps(forex_data)
            
            # Send alerts
            for pump in crypto_pumps + forex_pumps:
                if bot.chat_id:
                    user_id = bot.chat_id
                    if bot.user_settings.should_send_alert(user_id, pump):
                        await bot.send_pump_alert(pump)
                        bot.user_settings.record_alert_sent(user_id, pump, "Interactive alert")
                        bot.pump_detector.record_pump_alert(pump)
                else:
                    await bot.send_pump_alert(pump)
                    bot.pump_detector.record_pump_alert(pump)
            
            await asyncio.sleep(5)  # Check for updates every 5 seconds
            
        except Exception as e:
            logger.error(f"❌ Error in monitoring: {e}")
            await asyncio.sleep(10)

async def main():
    """Main function"""
    try:
        logger.info("🚀 Starting Simple Interactive Bot")
        
        # Initialize bot
        bot = SimpleInteractiveBot()
        
        # Send startup message
        await bot.send_startup_message()
        
        logger.info("✅ Simple interactive bot started successfully")
        
        # Start monitoring
        await run_monitoring_loop(bot)
        
    except KeyboardInterrupt:
        logger.info("🛑 Bot stopped by user")
    except Exception as e:
        logger.error(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
