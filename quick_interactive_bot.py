"""
Quick Interactive Bot with Buttons and Monitoring
"""
import asyncio
import sys
from pathlib import Path
from datetime import datetime
from loguru import logger
from telegram import Bot, Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from src.market_monitor import MarketMonitor
from src.pump_detector import PumpDetector
from src.user_settings import UserSettingsManager

class QuickInteractiveBot:
    """Quick interactive bot with monitoring and buttons"""
    
    def __init__(self):
        self.bot_token = Config.TELEGRAM_BOT_TOKEN
        self.chat_id = Config.TELEGRAM_CHAT_ID
        self.bot = None
        self.application = None
        self.user_settings = UserSettingsManager()
        self.market_monitor = MarketMonitor()
        self.pump_detector = PumpDetector()
        self._initialize_bot()
    
    def _initialize_bot(self):
        """Initialize the Telegram bot"""
        try:
            self.bot = Bot(token=self.bot_token)
            # Create application without job queue to avoid timezone issues
            from telegram.ext import ApplicationBuilder
            self.application = ApplicationBuilder().token(self.bot_token).build()
            
            # Add handlers
            self.application.add_handler(CommandHandler("start", self.start_command))
            self.application.add_handler(CommandHandler("menu", self.menu_command))
            self.application.add_handler(CallbackQueryHandler(self.button_callback))
            
            logger.info("✅ Quick interactive bot initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize bot: {e}")
            raise
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        keyboard = [
            [InlineKeyboardButton("📊 حالة البوت", callback_data="status"),
             InlineKeyboardButton("📈 إحصائياتي", callback_data="mystats")],
            [InlineKeyboardButton("🔔 الاشتراكات", callback_data="subscriptions"),
             InlineKeyboardButton("⚙️ الإعدادات", callback_data="settings")],
            [InlineKeyboardButton("❓ المساعدة", callback_data="help")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        message = """
🚀 **بوت مراقبة ضخ السيولة**

✅ البوت يعمل ويراقب الأسواق!
📊 10 عملات رقمية + 10 أزواج فوركس
🔔 تنبيهات فورية عند ضخ السيولة

**استخدم الأزرار للتحكم:**
        """
        await update.message.reply_text(message, parse_mode='Markdown', reply_markup=reply_markup)
    
    async def menu_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /menu command"""
        keyboard = [
            [InlineKeyboardButton("📊 حالة البوت", callback_data="status"),
             InlineKeyboardButton("📈 إحصائياتي", callback_data="mystats")],
            [InlineKeyboardButton("🔔 الاشتراكات", callback_data="subscriptions"),
             InlineKeyboardButton("⚙️ الإعدادات", callback_data="settings")],
            [InlineKeyboardButton("❓ المساعدة", callback_data="help")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text("🎛️ **القائمة الرئيسية**", parse_mode='Markdown', reply_markup=reply_markup)
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle button callbacks"""
        query = update.callback_query
        await query.answer()
        
        if query.data == "status":
            await self.show_status(query)
        elif query.data == "mystats":
            await self.show_mystats(query)
        elif query.data == "subscriptions":
            await self.show_subscriptions(query)
        elif query.data == "settings":
            await self.show_settings(query)
        elif query.data == "help":
            await self.show_help(query)
        elif query.data == "back":
            await self.show_menu(query)
    
    async def show_status(self, query):
        """Show bot status"""
        message = f"""
📊 **حالة البوت**

🟢 **الحالة:** يعمل ويراقب
⏰ **التحديث:** كل {Config.CHECK_INTERVAL_SECONDS} ثانية
📈 **العملات الرقمية:** {len(Config.CRYPTO_PAIRS)}
💱 **الفوركس:** {len(Config.FOREX_PAIRS)}

**الحدود:**
• الحجم: {Config.ALERT_THRESHOLDS['volume_spike']}x
• السعر: {Config.ALERT_THRESHOLDS['price_change']}%

⏰ **الآن:** {datetime.now().strftime('%H:%M:%S')}
        """
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)
    
    async def show_mystats(self, query):
        """Show user stats"""
        user_id = str(query.from_user.id)
        stats = self.user_settings.get_user_stats(user_id)
        
        message = f"""
📈 **إحصائياتك**

🔔 **التنبيهات:** {stats['total_alerts']}
📊 **الاشتراكات:** {stats['active_subscriptions']}
⏰ **آخر 24 ساعة:** {stats['recent_alerts_24h']}

📅 **اليوم:** {datetime.now().strftime('%Y-%m-%d')}
        """
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)
    
    async def show_subscriptions(self, query):
        """Show subscriptions"""
        message = """
🔔 **إدارة الاشتراكات**

**للاشتراك في عملة:**
`/subscribe BTC/USDT crypto`
`/subscribe EUR/USD forex`

**لإلغاء الاشتراك:**
`/unsubscribe BTC/USDT crypto`

**العملات المتاحة:**
🪙 BTC, ETH, BNB, ADA, SOL, XRP
💱 EUR/USD, GBP/USD, USD/JPY
        """
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)
    
    async def show_settings(self, query):
        """Show settings"""
        message = """
⚙️ **الإعدادات**

**لتعديل الحدود:**
`/threshold volume 3.5`
`/threshold price 7.0`
`/threshold confidence 0.8`

**الحدود الحالية:**
📊 الحجم: 3.0x
📈 السعر: 5.0%
🎯 الثقة: 0.7
        """
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)
    
    async def show_help(self, query):
        """Show help"""
        message = """
❓ **المساعدة**

**الأوامر:**
/start - بدء البوت
/menu - القائمة الرئيسية

**الاشتراكات:**
/subscribe [SYMBOL] [TYPE]
/unsubscribe [SYMBOL] [TYPE]

**الإعدادات:**
/threshold [TYPE] [VALUE]

**أمثلة:**
`/subscribe BTC/USDT crypto`
`/threshold volume 3.5`
        """
        
        keyboard = [[InlineKeyboardButton("🔙 القائمة", callback_data="back")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(message, parse_mode='Markdown', reply_markup=reply_markup)
    
    async def show_menu(self, query):
        """Show main menu"""
        keyboard = [
            [InlineKeyboardButton("📊 حالة البوت", callback_data="status"),
             InlineKeyboardButton("📈 إحصائياتي", callback_data="mystats")],
            [InlineKeyboardButton("🔔 الاشتراكات", callback_data="subscriptions"),
             InlineKeyboardButton("⚙️ الإعدادات", callback_data="settings")],
            [InlineKeyboardButton("❓ المساعدة", callback_data="help")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text("🎛️ **القائمة الرئيسية**", parse_mode='Markdown', reply_markup=reply_markup)
    
    async def send_pump_alert(self, pump_data):
        """Send pump alert with buttons"""
        try:
            symbol = pump_data.get('symbol', 'Unknown')
            market_type = pump_data.get('market_type', 'Unknown')
            price_change = pump_data.get('price_change_percent', 0)
            volume_spike = pump_data.get('volume_spike', 0)
            current_price = pump_data.get('current_price', 0)
            
            trend_emoji = "🚀" if price_change > 0 else "📉"
            market_emoji = "🪙" if market_type == "crypto" else "💱"
            
            message = f"""
{trend_emoji} **تنبيه ضخ سيولة!** {market_emoji}

**الرمز:** {symbol}
**السوق:** {market_type.upper()}
**السعر:** {current_price:.6f}
**التغيير:** {price_change:+.2f}%
**الحجم:** {volume_spike:.1f}x

⏰ {datetime.now().strftime('%H:%M:%S')}
            """
            
            keyboard = [
                [InlineKeyboardButton("📊 تفاصيل", callback_data=f"details_{symbol}"),
                 InlineKeyboardButton("🔕 إيقاف", callback_data=f"mute_{symbol}")],
                [InlineKeyboardButton("🎛️ القائمة", callback_data="back")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            if self.chat_id:
                await self.bot.send_message(
                    chat_id=self.chat_id,
                    text=message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                logger.info(f"📤 Interactive alert sent for {symbol}")
                
        except Exception as e:
            logger.error(f"❌ Failed to send alert: {e}")
    
    async def send_startup_message(self):
        """Send startup message"""
        try:
            keyboard = [[InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="back")]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            message = """
🚀 **بوت مراقبة ضخ السيولة**

✅ البوت يعمل الآن!
📊 يراقب الأسواق كل دقيقة
🔔 سيرسل تنبيهات تفاعلية

**اضغط الزر للوصول للقائمة:**
            """
            
            if self.chat_id:
                await self.bot.send_message(
                    chat_id=self.chat_id,
                    text=message,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
                logger.info("✅ Interactive startup message sent")
        except Exception as e:
            logger.error(f"❌ Failed to send startup message: {e}")

async def run_monitoring_loop(bot):
    """Monitoring loop"""
    logger.info("🔄 Starting interactive monitoring loop")
    
    while True:
        try:
            # Get market data
            crypto_data = await bot.market_monitor.get_crypto_data()
            forex_data = await bot.market_monitor.get_forex_data()
            
            # Detect pumps
            crypto_pumps = bot.pump_detector.detect_crypto_pumps(crypto_data)
            forex_pumps = bot.pump_detector.detect_forex_pumps(forex_data)
            
            # Send alerts
            for pump in crypto_pumps + forex_pumps:
                if bot.chat_id:
                    user_id = bot.chat_id
                    if bot.user_settings.should_send_alert(user_id, pump):
                        await bot.send_pump_alert(pump)
                        bot.user_settings.record_alert_sent(user_id, pump, "Interactive alert")
                        bot.pump_detector.record_pump_alert(pump)
                else:
                    await bot.send_pump_alert(pump)
                    bot.pump_detector.record_pump_alert(pump)
            
            await asyncio.sleep(Config.CHECK_INTERVAL_SECONDS)
            
        except Exception as e:
            logger.error(f"❌ Error in monitoring: {e}")
            await asyncio.sleep(30)

async def main():
    """Main function"""
    try:
        logger.info("🚀 Starting Quick Interactive Bot")
        
        # Initialize bot
        bot = QuickInteractiveBot()
        
        # Send startup message
        await bot.send_startup_message()
        
        # Start bot polling and monitoring
        await bot.application.initialize()
        await bot.application.start()
        await bot.application.updater.start_polling()
        
        # Start monitoring in background
        monitoring_task = asyncio.create_task(run_monitoring_loop(bot))
        
        logger.info("✅ Interactive bot started successfully")
        
        # Keep running
        try:
            await monitoring_task
        except KeyboardInterrupt:
            logger.info("🛑 Bot stopped by user")
        finally:
            await bot.application.stop()
            
    except Exception as e:
        logger.error(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
