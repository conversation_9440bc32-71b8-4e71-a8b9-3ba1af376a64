"""
إعداد سريع للبوت - Quick Bot Setup
"""

def setup_bot_config():
    """إعداد تكوين البوت"""
    print("🚀 إعداد بوت مراقبة ضخ السيولة")
    print("=" * 50)
    
    print("\n📝 يرجى إدخال المعلومات التالية:")
    
    # طلب التوكن
    print("\n1️⃣ توكن البوت (من BotFather):")
    print("   - اذهب إلى @BotFather في تيليجرام")
    print("   - أنشئ بوت جديد بالأمر /newbot")
    print("   - انسخ التوكن هنا")
    bot_token = input("   التوكن: ").strip()
    
    # طلب معرف المجموعة
    print("\n2️⃣ معرف المجموعة أو القناة:")
    print("   - أضف البوت للمجموعة كمدير")
    print("   - أرسل رسالة في المجموعة")
    print("   - استخدم @userinfobot للحصول على المعرف")
    chat_id = input("   معرف المجموعة: ").strip()
    
    # تأكيد معرف المدير
    print(f"\n3️⃣ معرف المدير: 772419417 (أسامة التبعي)")
    admin_confirm = input("   هل هذا صحيح؟ (y/n): ").strip().lower()
    
    if admin_confirm != 'y':
        admin_id = input("   أدخل معرف المدير الصحيح: ").strip()
    else:
        admin_id = "772419417"
    
    # إنشاء ملف التكوين
    config_content = f'''"""
بوت تيليجرام لمراقبة ضخ السيولة مع أزرار تفاعلية وإدارة الاشتراكات
Bot Telegram for Liquidity Pump Monitoring with Interactive Buttons and Subscription Management
"""

import asyncio
import json
import os
from datetime import datetime
from telegram import Bot, InlineKeyboardButton, InlineKeyboardMarkup

# ===== إعدادات البوت الأساسية =====
# Basic Bot Configuration
BOT_TOKEN = "{bot_token}"  # توكن البوت
CHAT_ID = "{chat_id}"      # معرف المجموعة
ADMIN_ID = "{admin_id}"    # أسامة التبعي - معرف المدير

# ===== قاعدة بيانات بسيطة للمستخدمين =====
# Simple Database for Users
USERS_FILE = "users_data.json"

def load_users_data():
    """تحميل بيانات المستخدمين من الملف"""
    try:
        if os.path.exists(USERS_FILE):
            with open(USERS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {{
            "subscribed_users": [],  # قائمة المستخدمين المشتركين
            "user_settings": {{}},     # إعدادات كل مستخدم
            "user_subscriptions": {{}} # اشتراكات كل مستخدم في العملات
        }}
    except:
        return {{"subscribed_users": [], "user_settings": {{}}, "user_subscriptions": {{}}}}

def save_users_data(data):
    """حفظ بيانات المستخدمين في الملف"""
    try:
        with open(USERS_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"خطأ في حفظ البيانات: {{e}}")

# تحميل البيانات عند بدء البوت
users_data = load_users_data()

# إضافة المدير تلقائياً للمستخدمين المشتركين
if ADMIN_ID not in users_data["subscribed_users"]:
    users_data["subscribed_users"].append(ADMIN_ID)
    users_data["user_settings"][ADMIN_ID] = {{
        "volume_threshold": 3.0,
        "price_threshold": 5.0,
        "notifications_enabled": True
    }}
    users_data["user_subscriptions"][ADMIN_ID] = []
    save_users_data(users_data)
    print(f"✅ تم إضافة المدير {{ADMIN_ID}} تلقائياً")

print("✅ تم تحميل إعدادات البوت بنجاح")
print(f"🤖 توكن البوت: {{BOT_TOKEN[:10]}}...")
print(f"💬 معرف المجموعة: {{CHAT_ID}}")
print(f"👑 معرف المدير: {{ADMIN_ID}}")
'''
    
    # حفظ الملف
    with open('bot_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("\n✅ تم إنشاء ملف التكوين بنجاح!")
    print("📁 الملف: bot_config.py")
    
    # إنشاء ملف تشغيل مبسط
    run_content = f'''"""
تشغيل البوت - Run Bot
"""

# استيراد التكوين
from bot_config import *
from bot_complete import LiquidityPumpBot
import asyncio

async def main():
    """تشغيل البوت الرئيسي"""
    print("🚀 بدء تشغيل بوت مراقبة ضخ السيولة...")
    print(f"👑 المدير: أسامة التبعي ({{ADMIN_ID}})")
    
    # إنشاء مثيل البوت
    bot = LiquidityPumpBot()
    
    # إرسال رسالة بدء التشغيل
    await bot.send_startup_message()
    
    print("✅ البوت يعمل الآن...")
    print("🎛️ جميع الأزرار التفاعلية تعمل!")
    print("👥 نظام إدارة الاشتراكات جاهز!")
    print("📞 للاشتراك تواصل مع أسامة التبعي: {{ADMIN_ID}}")
    
    # حلقة تشغيل البوت
    while True:
        try:
            await bot.process_updates()
            if bot.monitoring_active:
                pass  # هنا يمكن إضافة منطق مراقبة الأسواق
            await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("🛑 تم إيقاف البوت")
            break
        except Exception as e:
            print(f"❌ خطأ: {{e}}")
            await asyncio.sleep(5)

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    with open('run_bot.py', 'w', encoding='utf-8') as f:
        f.write(run_content)
    
    print("📁 ملف التشغيل: run_bot.py")
    
    print("\n🎯 خطوات التشغيل:")
    print("1. python run_bot.py")
    print("2. اختبر البوت في تيليجرام بالأمر /start")
    print("3. استخدم الأزرار التفاعلية للتنقل")
    
    print(f"\n📞 للاشتراك في البوت تواصل مع:")
    print(f"👤 أسامة التبعي")
    print(f"📱 Telegram ID: {admin_id}")
    print(f"📞 رقم الهاتف: {admin_id}")

if __name__ == "__main__":
    setup_bot_config()
