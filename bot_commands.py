"""
Additional command handlers for the complete interactive bot
"""
import asyncio
from datetime import datetime
from telegram import InlineKeyboardButton, InlineKeyboardMarkup
from loguru import logger

async def send_pump_alert(bot_instance, pump_data):
    """Send pump alert with interactive buttons"""
    try:
        symbol = pump_data.get('symbol', 'Unknown')
        market_type = pump_data.get('market_type', 'Unknown')
        price_change = pump_data.get('price_change_percent', 0)
        volume_spike = pump_data.get('volume_spike', 0)
        current_price = pump_data.get('current_price', 0)
        volume = pump_data.get('volume', 0)
        timestamp = pump_data.get('timestamp', datetime.now())
        
        trend_emoji = "🚀" if price_change > 0 else "📉"
        market_emoji = "🪙" if market_type == "crypto" else "💱"
        
        message = f"""
{trend_emoji} **تنبيه ضخ سيولة!** {market_emoji}

**الرمز:** {symbol}
**السوق:** {market_type.upper()}
**السعر الحالي:** {current_price:.6f}
**تغيير السعر:** {price_change:+.2f}%
**زيادة الحجم:** {volume_spike:.1f}x
**الحجم:** {volume:,.0f}

⏰ **الوقت:** {timestamp.strftime('%H:%M:%S')}
📅 **التاريخ:** {timestamp.strftime('%Y-%m-%d')}

#ضخ_سيولة #{symbol.replace('/', '_')} #{market_type}
        """
        
        keyboard = [
            [InlineKeyboardButton("📊 تفاصيل أكثر", callback_data=f"details_{symbol}")],
            [InlineKeyboardButton("🔕 إيقاف تنبيهات هذا الرمز", callback_data=f"mute_{symbol}")],
            [InlineKeyboardButton("🎛️ القائمة الرئيسية", callback_data="back_to_menu")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        if bot_instance.chat_id:
            await bot_instance.bot.send_message(
                chat_id=bot_instance.chat_id,
                text=message,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )
            logger.info(f"📤 Interactive pump alert sent for {symbol}")
            
    except Exception as e:
        logger.error(f"❌ Failed to send pump alert: {e}")

async def run_monitoring_loop(bot_instance):
    """Enhanced monitoring loop with full control"""
    logger.info("🔄 Starting complete interactive monitoring loop")
    
    while True:
        try:
            # Only monitor if active
            if bot_instance.monitoring_active:
                # Get market data
                crypto_data = await bot_instance.market_monitor.get_crypto_data()
                forex_data = await bot_instance.market_monitor.get_forex_data()
                
                # Detect pumps
                crypto_pumps = bot_instance.pump_detector.detect_crypto_pumps(crypto_data)
                forex_pumps = bot_instance.pump_detector.detect_forex_pumps(forex_data)
                
                # Send alerts
                for pump in crypto_pumps + forex_pumps:
                    if bot_instance.chat_id:
                        user_id = bot_instance.chat_id
                        if bot_instance.user_settings.should_send_alert(user_id, pump):
                            await send_pump_alert(bot_instance, pump)
                            bot_instance.user_settings.record_alert_sent(user_id, pump, "Interactive alert")
                            bot_instance.pump_detector.record_pump_alert(pump)
                    else:
                        await send_pump_alert(bot_instance, pump)
                        bot_instance.pump_detector.record_pump_alert(pump)
                
                logger.info(f"✅ Monitoring cycle completed - Active: {bot_instance.monitoring_active}")
                await asyncio.sleep(Config.CHECK_INTERVAL_SECONDS)
            else:
                # If monitoring is stopped, just wait
                logger.info("⏸️ Monitoring paused")
                await asyncio.sleep(10)
            
        except Exception as e:
            logger.error(f"❌ Error in monitoring loop: {e}")
            await asyncio.sleep(30)

async def main():
    """Main function to run the complete interactive bot"""
    try:
        logger.info("🚀 Starting Complete Interactive Bot")
        
        # Import here to avoid circular imports
        from complete_interactive_bot import CompleteInteractiveBot
        from config import Config
        
        # Initialize bot
        bot = CompleteInteractiveBot()
        
        # Send startup message
        await bot.send_startup_message()
        
        # Start bot application
        await bot.application.initialize()
        await bot.application.start()
        await bot.application.updater.start_polling()
        
        # Start monitoring in background
        monitoring_task = asyncio.create_task(run_monitoring_loop(bot))
        
        logger.info("✅ Complete interactive bot started successfully")
        logger.info("🎛️ All buttons and commands are now working!")
        
        # Keep running
        try:
            await monitoring_task
        except KeyboardInterrupt:
            logger.info("🛑 Bot stopped by user")
        finally:
            await bot.application.stop()
            
    except Exception as e:
        logger.error(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
