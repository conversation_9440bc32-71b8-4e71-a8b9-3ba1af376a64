"""
Test script for the Liquidity Pump Detection Bot
"""
import asyncio
import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from src.telegram_bot import TelegramBot
from src.market_monitor import MarketMonitor
from src.pump_detector import PumpDetector
from src.user_settings import UserSettingsManager

async def test_configuration():
    """Test bot configuration"""
    print("🔧 Testing configuration...")
    
    if not Config.validate_config():
        print("❌ Configuration validation failed")
        return False
    
    print("✅ Configuration is valid")
    return True

async def test_telegram_bot():
    """Test Telegram bot functionality"""
    print("📱 Testing Telegram bot...")
    
    try:
        bot = TelegramBot()
        
        # Test sending a message
        success = await bot.send_test_message("🧪 اختبار البوت - يعمل بشكل صحيح!")
        
        if success:
            print("✅ Telegram bot test successful")
            return True
        else:
            print("❌ Telegram bot test failed")
            return False
            
    except Exception as e:
        print(f"❌ Telegram bot error: {e}")
        return False

async def test_market_monitor():
    """Test market monitoring functionality"""
    print("📊 Testing market monitor...")
    
    try:
        async with MarketMonitor() as monitor:
            # Test crypto data
            crypto_data = await monitor.get_crypto_data()
            print(f"✅ Retrieved {len(crypto_data)} crypto pairs")
            
            # Test forex data
            forex_data = await monitor.get_forex_data()
            print(f"✅ Retrieved {len(forex_data)} forex pairs")
            
            if crypto_data or forex_data:
                print("✅ Market monitor test successful")
                return True
            else:
                print("⚠️ No market data retrieved")
                return False
                
    except Exception as e:
        print(f"❌ Market monitor error: {e}")
        return False

async def test_pump_detector():
    """Test pump detection functionality"""
    print("🔍 Testing pump detector...")
    
    try:
        detector = PumpDetector()
        
        # Create sample data for testing
        sample_crypto_data = [{
            'symbol': 'BTC/USDT',
            'market_type': 'crypto',
            'current_price': 50000,
            'volume_24h': 1000000,
            'price_change_percent_24h': 8.5,  # High change to trigger detection
            'timestamp': datetime.now(),
            'exchange': 'test'
        }]
        
        sample_forex_data = [{
            'symbol': 'EUR/USD',
            'market_type': 'forex',
            'current_price': 1.1000,
            'volume': 500000,
            'timestamp': datetime.now(),
            'exchange': 'test'
        }]
        
        # Test detection
        crypto_pumps = detector.detect_crypto_pumps(sample_crypto_data)
        forex_pumps = detector.detect_forex_pumps(sample_forex_data)
        
        print(f"✅ Pump detector test completed")
        print(f"   Crypto pumps detected: {len(crypto_pumps)}")
        print(f"   Forex pumps detected: {len(forex_pumps)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Pump detector error: {e}")
        return False

async def test_user_settings():
    """Test user settings functionality"""
    print("👤 Testing user settings...")

    try:
        user_settings = UserSettingsManager()
        test_user_id = "test_user_123"

        # Test getting default settings
        settings = user_settings.get_user_settings(test_user_id)
        print(f"✅ Retrieved user settings: {len(settings)} categories")

        # Test updating preference
        success = user_settings.update_user_preference(test_user_id, 'notifications.volume_threshold', 4.0)
        print(f"✅ Updated preference: {success}")

        # Test subscription management
        success = user_settings.add_user_subscription(test_user_id, 'BTC/USDT', 'crypto')
        print(f"✅ Added subscription: {success}")

        subscriptions = user_settings.get_user_subscriptions(test_user_id)
        print(f"✅ Retrieved {len(subscriptions)} subscriptions")

        # Test stats
        stats = user_settings.get_user_stats(test_user_id)
        print(f"✅ Retrieved user stats: {stats['total_alerts']} alerts")

        return True

    except Exception as e:
        print(f"❌ User settings error: {e}")
        return False

async def test_full_workflow():
    """Test the complete workflow"""
    print("🔄 Testing full workflow...")

    try:
        # Initialize components
        bot = TelegramBot()
        detector = PumpDetector()
        user_settings = UserSettingsManager()

        async with MarketMonitor() as monitor:
            # Get real market data
            crypto_data = await monitor.get_crypto_data()

            if crypto_data:
                # Test pump detection
                pumps = detector.detect_crypto_pumps(crypto_data[:1])  # Test with one pair

                if pumps:
                    # Test user settings filtering
                    test_user_id = Config.TELEGRAM_CHAT_ID or "test_user"
                    should_send = user_settings.should_send_alert(test_user_id, pumps[0])
                    print(f"✅ User settings check: {should_send}")

                    # Test sending alert
                    await bot.send_pump_alert(pumps[0])
                    print("✅ Full workflow test successful - alert sent!")
                else:
                    print("ℹ️ No pumps detected in current data (this is normal)")

                return True
            else:
                print("⚠️ No market data available for testing")
                return False

    except Exception as e:
        print(f"❌ Full workflow error: {e}")
        return False

async def main():
    """Main test function"""
    print("🧪 Starting Bot Tests")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("Telegram Bot", test_telegram_bot),
        ("Market Monitor", test_market_monitor),
        ("Pump Detector", test_pump_detector),
        ("User Settings", test_user_settings),
        ("Full Workflow", test_full_workflow),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Bot is ready to use.")
    else:
        print("⚠️ Some tests failed. Please check the configuration and try again.")

if __name__ == "__main__":
    asyncio.run(main())
