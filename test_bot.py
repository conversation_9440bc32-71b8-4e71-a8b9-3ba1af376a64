"""
اختبار البوت - Test Bot
"""

import asyncio
from bot_complete import LiquidityPumpBot

async def test_bot():
    """اختبار البوت"""
    print("🧪 بدء اختبار البوت...")
    
    try:
        # إنشاء مثيل البوت
        bot = LiquidityPumpBot()
        print("✅ تم إنشاء البوت بنجاح")
        
        # اختبار إرسال رسالة بدء التشغيل
        await bot.send_startup_message()
        print("✅ تم إرسال رسالة بدء التشغيل")
        
        print("🎛️ البوت جاهز للاستخدام!")
        print("📱 يمكنك الآن اختبار الأزرار في تيليجرام")
        
        # تشغيل البوت لمدة قصيرة للاختبار
        for i in range(10):
            await bot.process_updates()
            await asyncio.sleep(1)
            print(f"⏰ دورة {i+1}/10 - البوت يعمل...")
        
        print("✅ انتهى الاختبار بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    asyncio.run(test_bot())
