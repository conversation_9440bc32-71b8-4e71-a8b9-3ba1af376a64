bot_trading/
├── main.py                    # نقطة البداية الرئيسية
├── config.py                  # إعدادات البوت
├── requirements.txt           # المتطلبات
├── .env.example              # مثال على متغيرات البيئة
├── README.md                 # دليل شامل
├── QUICK_START.md            # دليل التشغيل السريع
├── setup.py                  # سكريبت الإعداد
├── setup.bat                 # إعداد Windows
├── run_bot.bat              # تشغيل Windows
├── test_bot.py              # اختبارات شاملة
├── performance_optimizer.py  # تحسين الأداء
└── src/
    ├── telegram_bot.py       # بوت تيليجرام
    ├── market_monitor.py     # مراقبة الأسواق
    ├── pump_detector.py      # كشف ضخ السيولة
    └── user_settings.py      # إدارة المستخدمين